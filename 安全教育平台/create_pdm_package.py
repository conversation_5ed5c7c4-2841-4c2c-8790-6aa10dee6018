#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建基于PDM的发布包
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_pdm_package():
    """创建基于PDM的发布包"""
    print("📦 创建基于PDM的发布包...")
    
    package_dir = Path('安全教育平台_PDM版')
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 复制核心文件
    core_files = [
        'app.py',
        'start_web.py', 
        'index.py',
        'database.py',
        '__init__.py',
        'pyproject.toml',
        'pdm.lock',
        'requirements.txt',
        'data.db'
    ]
    
    for file_name in core_files:
        if os.path.exists(file_name):
            shutil.copy2(file_name, package_dir)
            print(f"✅ 已复制: {file_name}")
    
    # 复制模板目录
    if os.path.exists('templates'):
        shutil.copytree('templates', package_dir / 'templates')
        print("✅ 已复制: templates")
    
    return package_dir

def create_pdm_launchers(package_dir):
    """创建PDM启动脚本"""
    print("🚀 创建PDM启动脚本...")
    
    # 主启动脚本
    main_launcher = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python环境
    echo 📥 请先安装Python 3.8或更高版本
    echo 🌐 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo 📦 检查PDM...
pdm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PDM未安装，正在自动安装...
    pip install pdm
    if %errorlevel% neq 0 (
        echo ❌ PDM安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ PDM安装完成
) else (
    echo ✅ PDM检查通过
)

echo.
echo 📦 安装项目依赖...
pdm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.

pdm run web

echo.
echo 👋 程序已退出，感谢使用！
pause
'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='utf-8') as f:
        f.write(main_launcher)
    
    # 快速启动脚本（假设已安装依赖）
    quick_launcher = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版 - 快速启动

echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000

pdm run web
'''
    
    with open(package_dir / '快速启动.bat', 'w', encoding='utf-8') as f:
        f.write(quick_launcher)
    
    # 安装依赖脚本
    install_script = '''@echo off
chcp 65001 >nul
title 安装依赖

echo.
echo ========================================
echo   安装项目依赖
echo ========================================
echo.

echo 📦 检查PDM...
pdm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PDM未安装，正在自动安装...
    pip install pdm
    if %errorlevel% neq 0 (
        echo ❌ PDM安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ PDM安装完成
)

echo.
echo 📦 安装项目依赖...
pdm install

echo.
echo ✅ 依赖安装完成！
echo 💡 现在可以使用 "快速启动.bat" 来启动程序
pause
'''
    
    with open(package_dir / '安装依赖.bat', 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    print("✅ PDM启动脚本已创建")

def create_pdm_readme(package_dir):
    """创建PDM版说明文档"""
    print("📖 创建说明文档...")
    
    readme_content = '''# 安全教育平台Web版 v2.0 - PDM版

## 🚀 快速开始

### 首次使用
1. 双击运行 `启动程序.bat`
2. 等待自动安装PDM和依赖包
3. 浏览器会自动打开Web界面

### 后续使用
1. 双击运行 `快速启动.bat`
2. 直接启动程序（跳过依赖检查）

### 手动安装依赖
1. 双击运行 `安装依赖.bat`
2. 等待依赖安装完成

## 🌟 功能特点

- ✅ **Web可视化界面** - 实时显示学习进度
- ✅ **多角色支持** - 支持学生、教师、管理员模式
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **PDM管理** - 使用现代化的PDM进行依赖管理
- ✅ **源码开放** - 可以查看和修改源代码

## 🎯 使用步骤

1. **启动程序** - 双击启动脚本
2. **打开界面** - 在浏览器中访问 http://localhost:5000
3. **填写信息** - 输入用户名、密码，选择角色
4. **开始学习** - 点击"开始任务"按钮
5. **查看进度** - 实时查看学习进度和日志

## 🎭 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生

## 📁 文件说明

- `启动程序.bat` - 完整启动脚本（包含依赖检查和安装）
- `快速启动.bat` - 快速启动脚本（跳过依赖检查）
- `安装依赖.bat` - 单独安装依赖脚本
- `app.py` - Web应用主文件
- `start_web.py` - Web启动脚本
- `index.py` - 核心业务逻辑
- `pyproject.toml` - PDM项目配置文件
- `templates/` - Web界面模板

## 🔧 故障排除

### 常见问题

1. **PDM安装失败**
   - 检查网络连接
   - 尝试手动安装：`pip install pdm`

2. **依赖安装失败**
   - 检查网络连接
   - 尝试切换pip源：`pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple`

3. **程序无法启动**
   - 确保Python 3.8+已安装
   - 检查防火墙设置

4. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查端口5000是否被占用

## 📋 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8或更高版本
- **内存**: 至少 2GB RAM
- **网络**: 稳定的互联网连接（用于安装依赖）

## 🛠️ 开发说明

### PDM命令

```bash
# 安装依赖
pdm install

# 启动Web界面
pdm run web

# 查看所有可用命令
pdm run --list
```

### 修改代码

所有源代码都可以直接查看和修改：
- 修改Web界面：编辑 `templates/index_new.html`
- 修改业务逻辑：编辑 `index.py`
- 修改Web应用：编辑 `app.py`

---

**版本**: v2.0 PDM版  
**更新时间**: 2024年  
**适用系统**: Windows 10/11  
**依赖管理**: PDM
'''
    
    with open(package_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 说明文档已创建")

def create_zip_package(package_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    # 删除旧的压缩包
    if os.path.exists(zip_name):
        os.remove(zip_name)
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 压缩包已创建: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🎯 创建安全教育平台PDM版发布包")
    print("=" * 50)
    
    # 创建PDM包
    package_dir = create_pdm_package()
    
    # 创建启动脚本
    create_pdm_launchers(package_dir)
    
    # 创建说明文档
    create_pdm_readme(package_dir)
    
    # 创建ZIP压缩包
    zip_file = create_zip_package(package_dir)
    
    print("\n🎉 PDM版发布包创建完成！")
    print(f"📁 发布包目录: {package_dir}")
    print(f"📦 压缩包: {zip_file}")
    print(f"📏 压缩包大小: {os.path.getsize(zip_file) / 1024 / 1024:.1f} MB")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件到任意目录")
    print("2. 双击运行 '启动程序.bat'")
    print("3. 等待自动安装依赖")
    print("4. 在浏览器中使用Web界面")
    print("\n✨ 特点:")
    print("- 🔧 使用PDM进行依赖管理")
    print("- 📖 源码开放，可以查看和修改")
    print("- 🚀 自动安装依赖")
    print("- 💡 提供多种启动方式")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n❌ 创建发布包失败: {e}")
        sys.exit(1)
