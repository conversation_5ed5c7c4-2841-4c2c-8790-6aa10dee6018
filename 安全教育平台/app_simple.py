#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from loguru import logger
import sys

# 导入原有的模块
from index import (
    Config, Student, Teacher, Admin,
    admin_learning, teacher_learning,
    _process_student_account, _complete_teacher_courses
)

# 导入数据库
from database import db

app = Flask(__name__)
app.config['SECRET_KEY'] = 'safety_education_platform_2024'

# 全局变量存储任务状态
task_status = {
    'running': False,
    'progress': 0,
    'total': 0,
    'current_task': '',
    'logs': [],
    'start_time': None,
    'end_time': None
}

# 日志存储
log_messages = []

class WebLogger:
    """Web日志处理器，将日志存储到内存"""
    
    def __init__(self):
        self.logs = []
    
    def write(self, message):
        if message.strip():
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = f"[{timestamp}] {message.strip()}"

            # 存储到全局日志列表
            log_messages.append(log_entry)
            self.logs.append(log_entry)
            task_status['logs'].append(log_entry)

            # 限制日志数量，避免内存溢出
            if len(log_messages) > 1000:
                log_messages.pop(0)
            if len(self.logs) > 1000:
                self.logs.pop(0)
            if len(task_status['logs']) > 1000:
                task_status['logs'].pop(0)
    
    def flush(self):
        pass

# 创建Web日志处理器
web_logger = WebLogger()

@app.route('/')
def index():
    """主页"""
    return render_template('index_new.html')

@app.route('/api/config', methods=['GET', 'POST'])
def handle_config():
    """处理配置信息"""
    if request.method == 'GET':
        # 读取配置文件
        try:
            config = Config.from_file('config.ini')
            return jsonify({
                'success': True,
                'data': {
                    'username': config.username,
                    'password': config.password,
                    'role': config.role,
                    'special_id': config.special_id,
                    'start_from_teacher': config.start_from_teacher,
                    'resume_from_breakpoint': config.resume_from_breakpoint
                }
            })
        except FileNotFoundError:
            return jsonify({
                'success': False,
                'message': '配置文件不存在'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'读取配置失败: {str(e)}'
            })
    
    elif request.method == 'POST':
        # 保存配置文件
        try:
            data = request.json
            config_content = f"""[Credentials]
username = {data['username']}
password = {data['password']}
role = {data['role']}
special_id = {data.get('special_id', '1178')}
start_from_teacher = {data.get('start_from_teacher', '')}
resume_from_breakpoint = {data.get('resume_from_breakpoint', False)}
"""
            with open('config.ini', 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            return jsonify({
                'success': True,
                'message': '配置保存成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'保存配置失败: {str(e)}'
            })

@app.route('/api/start', methods=['POST'])
def start_task():
    """启动学习任务"""
    if task_status['running']:
        return jsonify({
            'success': False,
            'message': '任务正在运行中，请等待完成'
        })
    
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')
        role = data.get('role')
        special_id = data.get('special_id', '1178')
        only_teacher = data.get('only_teacher', False)
        start_from_teacher = data.get('start_from_teacher', '')
        resume_from_breakpoint = data.get('resume_from_breakpoint', False)
        
        if not all([username, password, role]):
            return jsonify({
                'success': False,
                'message': '请填写完整的配置信息'
            })
        
        # 重置任务状态
        task_status.update({
            'running': True,
            'progress': 0,
            'total': 0,
            'current_task': '正在初始化...',
            'logs': [],
            'start_time': datetime.now(),
            'end_time': None
        })
        
        # 在新线程中执行任务
        thread = threading.Thread(
            target=run_learning_task,
            args=(username, password, role, special_id, only_teacher, start_from_teacher, resume_from_breakpoint)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': '任务已启动'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启动任务失败: {str(e)}'
        })

@app.route('/api/status')
def get_status():
    """获取任务状态"""
    return jsonify(serialize_task_status(task_status))

@app.route('/api/logs')
def get_logs():
    """获取历史日志"""
    return jsonify({
        'success': True,
        'logs': log_messages
    })

@app.route('/api/users', methods=['GET', 'POST'])
def manage_users():
    """管理用户"""
    if request.method == 'GET':
        # 获取所有保存的用户
        try:
            users = db.get_users()
            return jsonify({
                'success': True,
                'users': users
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取用户失败: {str(e)}',
                'users': []
            })

    elif request.method == 'POST':
        # 添加用户
        try:
            data = request.json
            name = data.get('name', '').strip()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
            role = data.get('role', 'student')

            if not all([name, username, password]):
                return jsonify({
                    'success': False,
                    'message': '请填写完整的用户信息'
                })

            user_id = db.add_user(name, username, password, role)
            if user_id:
                return jsonify({
                    'success': True,
                    'message': '用户添加成功',
                    'user_id': user_id
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '用户名已存在'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'添加用户失败: {str(e)}'
            })

@app.route('/api/users/<int:user_id>', methods=['PUT', 'DELETE'])
def manage_user(user_id):
    """管理单个用户"""
    if request.method == 'PUT':
        # 更新用户
        try:
            data = request.json
            name = data.get('name', '').strip()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
            role = data.get('role', 'student')

            if not all([name, username, password]):
                return jsonify({
                    'success': False,
                    'message': '请填写完整的用户信息'
                })

            success = db.update_user(user_id, name, username, password, role)
            if success:
                return jsonify({
                    'success': True,
                    'message': '用户更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '用户不存在或用户名已被使用'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新用户失败: {str(e)}'
            })

    elif request.method == 'DELETE':
        # 删除用户
        try:
            success = db.delete_user(user_id)
            if success:
                return jsonify({
                    'success': True,
                    'message': '用户删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '用户不存在'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除用户失败: {str(e)}'
            })

@app.route('/api/teachers')
def get_teachers():
    """获取缓存的教师列表"""
    try:
        admin_username = request.args.get('admin_username')
        teachers = db.get_teachers(admin_username)
        return jsonify({
            'success': True,
            'teachers': teachers
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取教师列表失败: {str(e)}',
            'teachers': []
        })

@app.route('/api/teachers/fetch', methods=['POST'])
def fetch_teachers():
    """通过管理员账号获取教师列表"""
    try:
        data = request.json
        admin_username = data.get('username')
        admin_password = data.get('password')

        if not admin_username or not admin_password:
            return jsonify({
                'success': False,
                'message': '请提供管理员账号和密码'
            })

        # 使用管理员账号登录并获取教师列表
        admin = Admin(admin_username, admin_password)
        admin.login()

        # 获取管理员的学校信息
        admin_info = admin.get_user_info()
        school_name = admin_info.get('schoolName', '')

        teacher_list = admin.get_teacher_list()

        # 转换教师数据格式
        formatted_teachers = []
        for teacher_info in teacher_list:
            formatted_teacher = {
                'username': teacher_info.get('username', ''),
                'nickname': teacher_info.get('nickname', ''),
                'school': school_name,  # 使用管理员的学校信息
                'class_count': teacher_info.get('class_count', 0),
                'student_count': teacher_info.get('student_count', 0)
            }
            formatted_teachers.append(formatted_teacher)

        # 保存教师列表到数据库
        db.save_teachers(formatted_teachers, admin_username)

        return jsonify({
            'success': True,
            'teachers': formatted_teachers,
            'message': f'成功获取 {len(formatted_teachers)} 个教师信息'
        })

    except Exception as e:
        logger.error(f'获取教师列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取教师列表失败: {str(e)}',
            'teachers': []
        })

@app.route('/api/teachers/clear', methods=['POST'])
def clear_teachers():
    """清空教师列表"""
    try:
        data = request.json
        admin_username = data.get('admin_username')

        count = db.clear_teachers(admin_username)
        return jsonify({
            'success': True,
            'message': f'已清空 {count} 个教师记录'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空教师列表失败: {str(e)}'
        })

def serialize_task_status(status):
    """序列化任务状态"""
    serialized = status.copy()
    if serialized['start_time']:
        serialized['start_time'] = serialized['start_time'].isoformat()
    if serialized['end_time']:
        serialized['end_time'] = serialized['end_time'].isoformat()
    return serialized

def run_learning_task(username, password, role, special_id, only_teacher, start_from_teacher, resume_from_breakpoint):
    """运行学习任务"""
    try:
        # 重定向输出到Web日志
        original_stdout = sys.stdout
        sys.stdout = web_logger
        
        task_status['current_task'] = '正在登录...'
        
        if role == 'student':
            student = Student(username, password)
            student.login()
            task_status['current_task'] = '开始学生学习任务'
            student.start_learning()
            
        elif role == 'teacher':
            teacher = Teacher(username, password)
            teacher.login()
            task_status['current_task'] = '开始教师学习任务'
            teacher_learning(teacher, start_from_teacher, resume_from_breakpoint)
            
        elif role == 'admin':
            admin = Admin(username, password)
            admin.login()
            task_status['current_task'] = '开始管理员学习任务'
            admin_learning(admin, only_teacher, start_from_teacher, resume_from_breakpoint)
        
        task_status['current_task'] = '任务完成'
        task_status['progress'] = 100
        
    except Exception as e:
        logger.error(f"任务执行失败: {str(e)}")
        task_status['current_task'] = f'任务失败: {str(e)}'
    finally:
        # 恢复原始输出
        sys.stdout = original_stdout
        task_status['running'] = False
        task_status['end_time'] = datetime.now()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
