#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建独立可执行文件版本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        subprocess.run(['pyinstaller', '--version'], capture_output=True, check=True)
        print("✅ PyInstaller已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装")
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 已清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"🧹 已清理文件: {spec_file}")

def build_standalone():
    """构建独立可执行文件"""
    print("🔨 构建独立可执行文件...")
    
    # PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=安全教育平台',
        '--add-data=templates;templates',
        '--add-data=data.db;.' if os.path.exists('data.db') else '',
        '--hidden-import=flask',
        '--hidden-import=flask_socketio',
        '--hidden-import=socketio',
        '--hidden-import=requests',
        '--hidden-import=loguru',
        '--hidden-import=bs4',
        '--hidden-import=lxml',
        '--hidden-import=urllib3',
        '--hidden-import=sqlite3',
        '--hidden-import=threading',
        '--hidden-import=webbrowser',
        '--hidden-import=time',
        '--hidden-import=pathlib',
        '--hidden-import=app',
        '--hidden-import=index',
        '--hidden-import=database',
        'start_web.py'
    ]
    
    # 移除空的参数
    cmd = [arg for arg in cmd if arg]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def create_standalone_package():
    """创建独立版本包"""
    print("📦 创建独立版本包...")
    
    package_dir = Path('安全教育平台_独立版')
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 复制可执行文件
    exe_file = Path('dist/安全教育平台.exe')
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir)
        print(f"✅ 已复制: {exe_file.name}")
    else:
        print("❌ 找不到可执行文件")
        return False
    
    return package_dir

def create_launchers(package_dir):
    """创建启动脚本"""
    print("🚀 创建启动脚本...")
    
    # 主启动脚本
    main_launcher = '''@echo off
chcp 65001 >nul
title 安全教育平台

echo.
echo ========================================
echo   安全教育平台 v2.0
echo ========================================
echo.

echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo.

cd /d "%~dp0"
start "安全教育平台" "%~dp0安全教育平台.exe"

echo ✅ 程序已启动
echo 📱 请在浏览器中访问: http://localhost:5000
echo.
echo 💡 提示：
echo    • 如果浏览器没有自动打开，请手动访问上述地址
echo    • 关闭此窗口不会停止程序
echo    • 如需停止程序，请关闭程序窗口
echo.
pause
'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='utf-8') as f:
        f.write(main_launcher)
    
    # 静默启动脚本
    silent_launcher = '''@echo off
cd /d "%~dp0"
start "" "%~dp0安全教育平台.exe"
'''
    
    with open(package_dir / '静默启动.bat', 'w', encoding='utf-8') as f:
        f.write(silent_launcher)
    
    print("✅ 启动脚本已创建")

def create_readme(package_dir):
    """创建使用说明"""
    print("📖 创建使用说明...")
    
    readme_content = '''# 安全教育平台 v2.0 - 独立版

## 🚀 快速开始

### 推荐方式
1. 双击 `启动程序.bat`
2. 等待程序启动
3. 在浏览器中访问 http://localhost:5000

### 其他方式
- `静默启动.bat` - 后台启动，不显示窗口
- 直接双击 `安全教育平台.exe`

## 🌟 功能特点

- ✅ **独立可执行** - 无需安装Python或任何依赖
- ✅ **Web可视化界面** - 实时显示学习进度
- ✅ **多角色支持** - 支持学生、教师、管理员模式
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **一键启动** - 双击即可运行
- ✅ **绿色软件** - 解压即用，无需安装

## 🎯 使用步骤

1. **启动程序** - 双击启动脚本或可执行文件
2. **打开界面** - 在浏览器中访问 http://localhost:5000
3. **填写信息** - 输入用户名、密码，选择角色
4. **开始学习** - 点击"开始任务"按钮
5. **查看进度** - 实时查看学习进度和日志

## 🎭 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查是否有杀毒软件拦截
   - 确保Windows系统版本兼容
   - 尝试以管理员身份运行

2. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查防火墙设置
   - 确认端口5000未被占用

3. **登录失败**
   - 检查账号密码是否正确
   - 确认网络连接正常
   - 检查是否频繁登录被限制

## 📋 系统要求

- **操作系统**: Windows 10/11
- **内存**: 至少 2GB RAM
- **网络**: 稳定的互联网连接
- **浏览器**: Chrome、Firefox、Edge等现代浏览器

## 🔒 安全说明

- 本程序已进行代码编译处理
- 不会收集或泄露用户个人信息
- 仅用于自动化学习，请合理使用

---

**版本**: v2.0 独立版  
**更新时间**: 2024年  
**兼容系统**: Windows 10/11  
**特点**: 无需Python环境，开箱即用
'''
    
    with open(package_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 使用说明已创建")

def create_zip_package(package_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    # 删除旧的压缩包
    if os.path.exists(zip_name):
        os.remove(zip_name)
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 压缩包已创建: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🎯 安全教育平台独立版打包工具")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建独立可执行文件
    if not build_standalone():
        return False
    
    # 创建独立版本包
    package_dir = create_standalone_package()
    if not package_dir:
        return False
    
    # 创建启动脚本
    create_launchers(package_dir)
    
    # 创建使用说明
    create_readme(package_dir)
    
    # 创建ZIP压缩包
    zip_file = create_zip_package(package_dir)
    
    print("\n🎉 独立版打包完成！")
    print(f"📁 独立版目录: {package_dir}")
    print(f"📦 压缩包: {zip_file}")
    print(f"📏 压缩包大小: {os.path.getsize(zip_file) / 1024 / 1024:.1f} MB")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件到任意目录")
    print("2. 双击运行 '启动程序.bat' 或 '安全教育平台.exe'")
    print("3. 在浏览器中使用Web界面")
    print("\n✨ 特点:")
    print("- 🚀 无需Python环境，开箱即用")
    print("- 📦 单文件可执行，包含所有依赖")
    print("- 🌐 Web界面，操作简单直观")
    print("- 💚 绿色软件，解压即用")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        sys.exit(1)
