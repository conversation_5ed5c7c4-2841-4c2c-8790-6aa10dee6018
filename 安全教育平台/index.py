import configparser
import time
import requests
import json
import re
import random
from functools import reduce
from datetime import datetime
from loguru import logger
from bs4 import BeautifulSoup
import urllib3
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from pathlib import Path

urllib3.disable_warnings()

__all__ = [
    'main'
]

@dataclass
class Config:
    """配置类，用于存储用户配置信息"""
    username: str
    password: str
    role: str
    special_id: str = '1178'
    start_from_teacher: str = ''
    resume_from_breakpoint: bool = False
    default_password: str = 'WCLhqxx2022'

    @classmethod
    def from_file(cls, file_path: str = 'config.ini') -> 'Config':
        """从配置文件读取配置"""
        config = configparser.ConfigParser()
        config.read(file_path, encoding='utf-8')
        credentials = config['Credentials']
        return cls(
            username=credentials['username'],
            password=credentials['password'],
            role=credentials['role'],
            special_id=credentials.get('special_id', '1178'),
            start_from_teacher=credentials.get('start_from_teacher', ''),
            resume_from_breakpoint=credentials.getboolean('resume_from_breakpoint', fallback=False)
        )


class BaseAPI:
    """基础API类，提供通用的网络请求功能"""

    def __init__(self):
        self.session = self._create_session()
        self.land_marks = ''

    def _create_session(self) -> requests.Session:
        """创建并配置session"""
        session = requests.Session()
        session.verify = False
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
        })
        return session

    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """统一的请求方法，包含错误处理和重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()
                return response
            except requests.RequestException as e:
                if attempt == max_retries - 1:
                    logger.error(f"请求失败: {url}, 错误: {e}")
                    raise
                time.sleep(1)  # 重试前等待1秒

    def get_prv_list(self) -> List[Dict]:
        """获取省份信息列表"""
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/prv-list"
        response = self._make_request('GET', url).json()
        self.land_marks = response['Landmarks']
        return response['Data']

    def get_city_list(self, prv_id: int) -> List[Dict]:
        """获取城市信息列表"""
        url = "https://chongqing.xueanquan.com/WebApi/api/area/city-list"
        params = {"jsoncallback": "", "prvId": prv_id}
        response = self._make_request('GET', url, params=params)
        return response.json()

    def get_country_list(self, city_id: int) -> List[Dict]:
        """获取地区信息列表"""
        url = "https://chongqing.xueanquan.com/WebApi/api/area/county-list"
        params = {"jsoncallback": "", "cityId": city_id}
        response = self._make_request('GET', url, params=params)
        return response.json()

    def get_school_list(self, country_id: int) -> List[Dict]:
        """获取学校信息列表"""
        url = "https://chongqing.xueanquan.com/WebApi/api/area/school-list"
        params = {"jsoncallback": "", "countryId": country_id}
        response = self._make_request('GET', url, params=params)
        return response.json()

    def get_grade_list(self, school_id: int) -> List[Dict]:
        """获取年级信息列表"""
        url = "https://chongqing.xueanquan.com/WebApi/api/area/grade-list"
        params = {"jsoncallback": "", "schoolId": school_id}
        response = self._make_request('GET', url, params=params)
        return response.json()

    def get_class_list(self, school_id: int, grade_id: int) -> List[Dict]:
        """获取班级信息列表"""
        url = "https://chongqing.xueanquan.com/WebApi/api/area/classroom-list"
        params = {"jsoncallback": "", "schoolId": school_id, "grade": grade_id}
        response = self._make_request('GET', url, params=params)
        return response.json()


class User(BaseAPI):
    """用户基类，提供登录和基础用户操作功能"""

    def __init__(self, username: str = '', password: str = ''):
        super().__init__()
        self.username = username.strip()
        self.password = password.strip()
        self.nickname = '未知'
        self.is_success = False
        self.token = ''

    def get_user_id(self, teacher_info: dict):
        """
        获取用户ID 如：7B94E3F4A0985AE82BC111270569F10421AED32C031B955A
        :return:{
            "Status": true,
            "UserName": "ligongxun2883",
            "DesUserId": "7B94E3F4A0985AE82BC111270569F10421AED32C031B955A"
        }
        """

        self.get_prv_list()
        time.sleep(0.2)
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/select-userinfo"
        querystring = {
            "TrueName": teacher_info['trueName'],
            "UserType": teacher_info.get('UserType', 0),
            "Prv": teacher_info['prvCode'],
            "Account": teacher_info['userName'],
            "City": teacher_info['cityCode'],
            "Country": teacher_info['countyId'],
            "School": teacher_info['schoolId'],
            "Grade": teacher_info['grade'],
            "ClassRoom": teacher_info['classRoom']
        }
        response = self.session.get(url, params=querystring, headers={
            'Landmarks': self.land_marks
        })
        data = response.json()
        self.land_marks = data['Landmarks']
        return data['Data']['DesUserId']

    def get_user_success_code(self, user_name, user_id):
        """
        获取用户成功码
        :param user_name:ligongxun2883
        :param user_id: 7B94E3F4A0985AE82BC111270569F10421AED32C031B955A
        :return:{
            "Result": true,
            "Message": "DFA897B0172D13244F49C5B41018CD54B9E9B637CF97E519"
        }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/find-usercard"
        querystring = {
            "UseName": user_name,
            "DescUserId": user_id
        }
        response = self.session.post(url, data=querystring, headers={
            "Landmarks": self.land_marks
        })
        data = response.json()
        self.land_marks = data['Landmarks']
        return data['Message']

    def get_user_info(self) -> dict:
        url = "https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/users/user-info"
        resp = self.session.get(url)
        userinfo = resp.json()
        return userinfo

    def login(self, username: str = '', password: str = '', is_logger: bool = True) -> requests.Session:
        """用户登录"""
        url = "https://appapi.xueanquan.com/usercenter/api/v3/wx/login"
        params = {"checkShowQrCode": "true", "tmp": "false"}
        payload = {
            "username": username or self.username,
            "password": password or self.password,
            "loginOrigin": 1
        }
        headers = {
            "Accept-Encoding": "deflate, gzip",
            "content-type": "application/json"
        }

        try:
            resp = self.session.post(url=url, json=payload, headers=headers, params=params)
            data = resp.json()

            if data['err_code'] == 8:
                logger.error(f"{self.username}\t{data['err_desc']}")
                if '登录失败' in data['err_desc']:
                    logger.warning('登录太频繁，休息15分钟后继续')
                    time.sleep(15 * 60)
                    return self.login(username, password, is_logger)
                else:
                    raise Exception(data['err_desc'])
            elif data['err_code'] != 0:
                logger.error(f"{self.username}\t{data['err_desc']}")
                raise Exception(data['err_desc'])

            # 登录成功
            self.session.headers['Authorization'] = data['data']['token']
            self.token = data['data']['token']
            self.nickname = data['data']['trueName']
            self.is_success = True

            if is_logger:
                logger.success(f'{self.nickname}\t登录成功')
            return self.session

        except Exception as e:
            logger.error(f"登录失败: {e}")
            raise

    def _get_password_params(self, teacher_info: dict):
        user_id = self.get_user_id(teacher_info)
        time.sleep(0.5)
        success_code = self.get_user_success_code(user_name=teacher_info['userName'], user_id=user_id)
        time.sleep(0.5)
        return {
            "user_id": user_id,
            "success_code": success_code
        }

    def change_password(self, teacher_info: Dict, password: str = 'WCLhqxx2022') -> Dict:
        """修改密码"""
        user_info = self._get_password_params(teacher_info)
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/PwdChangeByUserSecurity"
        data = {
            "newpwd": password,
            "DescUserId": user_info['user_id'],
            "DesSuccess": user_info['success_code']
        }

        try:
            response = self.session.post(url, data=data, headers={
                "Landmarks": self.land_marks
            }).json()

            if response.get('ErrorMsg', '').find('10分钟') != -1:
                logger.error(f"{self.username}\t{response.get('ErrorMsg')}")
                logger.info('需要等待10分钟后重试')
                time.sleep(60 * 10)
                return self.change_password(teacher_info, password)

            return response
        except Exception as e:
            logger.error(f"修改密码失败: {e}")
            raise

    def _get_special_id(self, course_url: str):
        """
        获取专题学习id
        :return:
        """
        # 通过js文件查找
        course_id = course_url.split('/')[-2]
        url = f'https://huodong.xueanquan.com/{course_id}/js/common.js'
        resp = self.session.get(url)
        specialid = re.findall(r'specialId: (\d+),', resp.text)
        if len(specialid) > 0:
            return specialid[-1]
        # 找不到就去页面body中查找
        resp = self.session.get(course_url.replace('index.html', 'xuexiao.html'))
        soup = BeautifulSoup(resp.content, 'lxml')
        specialid = soup.body.get('data-specialid')
        if not specialid:
            raise Exception('获取specialid失败')
        return specialid
        # return '1152'


class Student(User):
    """学生类，处理学生相关的学习任务"""

    def complete_all_courses(self) -> None:
        """完成所有课程"""
        try:
            course_list = self.get_courses()
            if not course_list:
                logger.info(f'{self.nickname}\t没有待完成的课程')
                return

            for course in course_list:
                self._complete_course_by_type(course)
                time.sleep(0.5)  # 避免请求过于频繁

        except Exception as e:
            logger.error(f'{self.nickname}\t完成课程时出错: {e}')
            raise

    def _complete_course_by_type(self, course: Dict) -> None:
        """根据课程类型完成相应课程"""
        course_type = course.get('sort', '')
        course_title = course.get('title', '未知课程')

        try:
            if course_type == 'Skill':
                self.complete_skill_course(course)
            elif course_type == 'Special':
                self.complete_special_course(course)
            elif course_type == 'SummerWinterHoliday':
                self.complete_holiday_course(course)
            else:
                logger.warning(f'{self.nickname}\t未知课程类型: {course_type}, 课程: {course_title}')
        except Exception as e:
            logger.error(f'{self.nickname}\t完成课程 {course_title} 失败: {e}')

    def _complete_video(self, course_id, grade_id):
        url = "https://yyapi.xueanquan.com/chongqing/api/v1/StudentHomeWork/VideoPlayRecordSave"
        querystring = {"courseId": course_id, "gradeId": grade_id}
        self.session.post(url, params=querystring)
        pass

    def _get_video_info(self, course_id):
        url = "https://yyapi.xueanquan.com/chongqing/api/v1/StudentHomeWork/VideoInfoGet"
        querystring = {"courseId": course_id}
        data = self.session.get(url, params=querystring).json()['result']
        return {
            "fid": data['fid'],
            'work_id': data['workId']
        }

    def complete_holiday_course(self, course):
        now = datetime.now()
        url = "https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/holiday/sign"
        special_id = self._get_special_id(course['url'])
        for step in range(1, 3):
            payload = {
                "schoolYear": now.year,
                "semester": 2 if now.month > 5 else 1,
                "step": step,
                'specialId': special_id
            }
            resp = self.session.post(url, json=payload, headers={
                'Referer': 'https://huodong.xueanquan.com/'
            }).json()
        if (resp.get('result')):
            logger.success(f'{self.nickname}\t{course["title"]}\t{resp.get("msg", "学习完毕")}')
        else:
            logger.error(f'{self.nickname}\t{course["title"]}{resp.get("msg", "学习失败")}')

    def complete_skill_course(self, course):
        """
        完成安全学习
        :return:
        """
        course_id = re.findall(r'li=(\d+)', course['url'])[0]
        grade_id = re.findall(r'gid=(\d+)', course['url'])[0]
        video_info = self._get_video_info(course_id)
        self._complete_video(course_id, grade_id)
        url = "https://yyapi.xueanquan.com/chongqing/api/v1/StudentHomeWork/HomeWorkSign"
        payload = {
            "workId": video_info['work_id'],
            "fid": video_info['fid'],
            "testanswer": "0|0|0",
            "testinfo": "已掌握技能",
            "testMark": 100,
            "testResult": 1,
            "courseId": course_id,
        }
        resp = self.session.post(url, json=payload).json()
        if resp['success']:
            logger.success(f'{self.nickname}\t{course["title"]}学习完毕')
        else:
            logger.error(f'{self.nickname}\t{course["title"]}学习失败')
        pass

    def complete_special_course(self, course):
        """
        完成专题学习
        :return:
        """
        headers = {
            'Referer': 'https://huodong.xueanquan.com/'
        }
        url = 'https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/records/sign'
        # self.session.get(course['url'])
        special_id = self._get_special_id(course['url'])
        temp1 = self.session.post(url, data={
            "specialId": special_id,
            "step": 1
        }, headers=headers)
        temp2 = self.session.post(url, data={
            "specialId": special_id,
            "step": 2
        }, headers=headers)
        try:
            self.submit_special_test(special_id)
        except IndexError as e:
            logger.info('跳过专题测试问卷')
        # self.session.get(course['url'])
        if self.check_finish(special_id):
            logger.success(f'{self.nickname} \t {course["title"]}\t完成签到')
        else:
            logger.error(f'{self.nickname} \t {course["title"]}\t签到失败')
        pass

    def submit_special_test(self, special_id):
        """
        提交专题测试问卷
        :return:
        """
        question = self._get_special_question(special_id)
        user = self.get_user_info()
        post_data = {
            'user': user,
            'UserAnswers': question,
            'specialId': special_id,
            'step': 2,
            "countyId": user['countyId']
        }
        url = 'https://huodongapi.xueanquan.com/Topic/topic/main/api/v1/records/v3/survey'
        self.session.post(url, json=post_data)

    @staticmethod
    def _choice_answer(item):
        """
        从选项中随机选择专题测试答案
        :param item:
        :return:
        """
        if item['radioType'] == 'Radio':
            return random.choice(item['option'])['item']
        return ','.join(list(map(lambda x: x['item'], random.choices(item['option']))))

    def _get_special_question(self, special_id):
        """
        获取专题测试问题
        :return:
        """
        url = "https://huodongapi.xueanquan.com/Topic/topic/main/api/v1/records/questions"
        querystring = {"specialId": special_id, "grade": "2", "region": "0"}
        resp = self.session.get(url, params=querystring)
        data = resp.json()['result'][0]
        question_list = list(map(lambda item: {
            'module': data['module'],
            'id': item['id'],
            'type': item['radioType'],
            'answer': self._choice_answer(item),
            'tagId': item['tagId'],
        }, data['question']))
        pass
        return question_list

    def get_courses(self) -> list:
        """
        获取未完成课程
        :return: List
        """
        url = 'https://yyapi.xueanquan.com/chongqing/safeapph5/api/v1/homework/homeworklist'
        resp = self.session.get(url)
        # 过滤出未完成课程
        return list(filter(lambda item: item.get('workStatus', '') == 'UnFinish', resp.json()))
        pass

    def check_finish(self, special_id):
        """
        检测学生是否完成
        :param special_id:
        :return:
        """
        url = 'https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/records/finish-status'
        resp = self.session.get(url, params={
            "specialId": special_id
        }).json()
        if resp:
            return resp['finishStatus']
        return False


class Teacher(User):
    def get_student_list(self, page=1):
        """
        获取学生账号等信息
        :return:
        """
        data_list = []
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement"
        resp = self.session.post(url, data={"pageNum": page, 'numPerPage': 100})
        soup = BeautifulSoup(resp.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            data_list.append({
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'status': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1]
            })
        return data_list

    def get_teach_course_ids(self, user_info: dict) -> list:
        """
        获取已学习课程ID
        :param user_info:
        :return:
        """
        url = "https://chongqing.xueanquan.com/ajax/EPlatform.SafeSchool.TeacherCourse,EPlatform.ashx"
        querystring = {"_method": "TeaCourse", "_session": "rw"}
        payload = f"userid={user_info['userID']}\r\nclassroom={user_info['classRoom']}\r\ngg={user_info['grade']}"
        resp = self.session.post(url, data=payload, params=querystring)
        data = json.loads(resp.text.replace('\'', '\"'))
        return list(map(lambda item: item['courseid'], data['Rows']))
        pass

    def get_courses(self) -> list:
        """
        获取未完成课程
        :return:
        """
        grade_id = self._get_grade_id()
        user_info = self.get_user_info()
        teach_course_ids = self.get_teach_course_ids(user_info)
        url = "https://chongqing.xueanquan.com/ajax/EPlatform.SafeSchool.TeacherCourse,EPlatform.ashx"
        resp = self.session.post(url, params={"_method": "I_CoursesGet"},
                                 data=f"gradeid={grade_id}\r\ngrade={user_info['grade']}")
        data = json.loads(resp.text.replace('\'', '\"').replace('new Date(1,0,1,0,0,0)', '""'))
        # data_list = reduce(lambda x, y: x['Courses'] + y['Courses'], data)
        all_course_list = reduce(lambda x, y: x + y['Courses'], data, [])
        return list(filter(lambda item: item['CourseID'] not in teach_course_ids, all_course_list))

    def _get_grade_id(self):
        url = 'https://chongqing.xueanquan.com/SafeSchool/TeacherCourse.aspx'
        resp = self.session.get(url)
        return re.findall(r'grade\s*=\s*"(\d+)"', resp.text)[0]

    def _get_fid(self, course_id: str):
        url = "https://yyapi.xueanquan.com/chongqing/ajax/EPlatform.SafeSchool.TeacherCourseText,EPlatform.aspx"
        querystring = {"_method": "TempGetByCourseID"}
        resp = self.session.post(url, json=course_id, params=querystring).json()
        return resp['fid']

    def complete_skill_course(self, course_info: dict):
        """
        完成安全授课
        :return:
        """
        url = "https://yyapi.xueanquan.com/chongqing/ajax/EPlatform.SafeSchool.TeacherCourseText,EPlatform.aspx"
        querystring = {"_method": "InsertCourseStatus_New"}
        payload = {
            "couserId": course_info['CourseID'],
            "gradeId": course_info['GradeID'],
            "fid": self._get_fid(course_info['CourseID']),
            "title": course_info['CourseName']
        }
        response = self.session.post(url, json=payload, params=querystring)
        logger.info(response.text)

    def _get_special_url(self) -> str:
        # resp = self.session.get('https://chongqing.xueanquan.com/MainPage.html')
        # soup = BeautifulSoup(resp.text, 'lxml')
        resp = self.session.get(
            f'https://put.xueanquan.com/angel/push/imageslide.html?location=1&siteid=0&r={int(time.time() / 3600)}&host=chongqing.xueanquan.com')
        soup = BeautifulSoup(resp.text, 'lxml')
        return soup.select('a')[0].get('href', '')

    def complete_special_course(self, special_id=None):
        """
        完成专题授课
        :return:
        """
        try:

            headers = {
                'Referer': 'https://huodong.xueanquan.com/'
            }
            url = 'https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/records/sign'
            # self.session.get(course['url'])
            if not special_id:
                special_url = self._get_special_url()
                special_id = self._get_special_id(special_url)
            temp1 = self.session.post(url, data={
                "specialId": special_id,
                "step": 1
            }, headers=headers)
            temp2 = self.session.post(url, data={
                "specialId": special_id,
                "step": 2
            }, headers=headers)
            logger.success(f'教师{self.nickname} \t 完成签到')
        except IndexError as e:
            logger.error(e)

    def check_finish(self) -> bool:
        """
        检测教师是否完成授课
        :return:
        """
        url = 'https://chongqing.xueanquan.com/ajax/EPlatform.SafeSchool.TeacherCourseText,EPlatform.ashx'
        resp = self.session.post(url, params={"_method": "TeachStudySituationProgress", "_session": "rw"},
                                 data=f"liID=593\r\ngid=486")
        data = json.loads(resp.text.replace('\'', '\"'))
        return bool(data['Rows'][0]['skillStatus'])

    def change_teacher_password(self, password='Mxy012522', old_password=''):
        url = 'https://chongqing.xueanquan.com/eduadmin/UserInfo/UserPwdModifySave'
        resp = self.session.post(url, data={
            'OldPWD': old_password or self.password,
            'NewPWD1': password,
            'NewPWD2': password,
            'ajax': 1
        })
        if resp.ok:
            logger.success(resp.json()['message'])
            return resp.json()
        pass

    def reset_student_password(self, student_id, landmarks):
        """
        重置学生密码
        :param landmarks:
        :param student_id:
        :return:
        """
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/StudentPassWordReset"
        querystring = {"studentid": student_id}
        resp = self.session.post(url, params=querystring, headers={
            'landmarks': landmarks,
        }).json()
        pass


class Admin(Teacher):
    def reset_teacher_password(self, student_id, landmarks):
        pass

    def get_projects_list(self):
        """
        获取项目列表
        :return:
        """
        projects = []
        url = "https://chongqing.xueanquan.com/EduAdmin/Home/Index#SportInfo_SportInfo_JSGZ_7"

        try:
            # 访问项目页面
            resp = self.session.get("https://chongqing.xueanquan.com/EduAdmin/Home/Index")
            resp.encoding = 'utf-8'
            soup = BeautifulSoup(resp.text, 'lxml')

            # 查找项目链接
            project_links = soup.select('a[href*="SpecialInfo"]')

            for link in project_links:
                title = link.get('title', '').strip()
                href = link.get('href', '')

                # 跳过教师授课情况
                if '教师授课情况' in title or not title:
                    continue

                project = {
                    'title': title,
                    'url': href,
                    'special_id': '',
                    'status': 'disabled',  # 默认禁用，需要解析special_id后启用
                    'teacher_total': 0,
                    'teacher_completed': 0,
                    'teacher_completion_rate': 0,
                    'student_total': 0,
                    'student_completed': 0,
                    'student_completion_rate': 0,
                    'teacher_details': []
                }

                # 尝试获取special_id和完成情况
                try:
                    if href.startswith('/'):
                        full_url = f"https://chongqing.xueanquan.com{href}"
                    else:
                        full_url = href

                    # 访问项目详情页面
                    detail_resp = self.session.get(full_url)
                    detail_resp.encoding = 'utf-8'
                    detail_soup = BeautifulSoup(detail_resp.text, 'lxml')

                    # 查找form表单的action属性
                    form = detail_soup.select_one('form[action*="SpecialInfo"]')
                    if form:
                        action = form.get('action', '')
                        # 从action中提取SpecialID
                        import re
                        match = re.search(r'SpecialID=([^&]+)', action)
                        if match:
                            project['special_id'] = match.group(1)
                            project['status'] = 'active'  # 有special_id的项目启用

                            # 解析完成情况数据
                            completion_data = self._parse_completion_data(detail_soup)
                            project.update(completion_data)

                            logger.info(f'项目 {title} 解析成功，special_id: {project["special_id"]}')
                        else:
                            logger.warning(f'项目 {title} 未找到SpecialID')
                    else:
                        logger.warning(f'项目 {title} 未找到form表单')

                except Exception as e:
                    logger.error(f'解析项目 {title} 失败: {str(e)}')

                projects.append(project)

        except Exception as e:
            logger.error(f'获取项目列表失败: {str(e)}')

        return projects

    def _parse_completion_data(self, soup):
        """解析项目完成情况数据"""
        completion_data = {
            'teacher_total': 0,
            'teacher_completed': 0,
            'teacher_completion_rate': 0,
            'student_total': 0,
            'student_completed': 0,
            'student_completion_rate': 0,
            'teacher_details': []
        }

        try:
            # 查找统计表格
            table = soup.select_one('table')
            if not table:
                return completion_data

            rows = table.select('tr')[1:]  # 跳过表头
            teacher_details = []

            for row in rows:
                cells = row.select('td')
                if len(cells) >= 8:  # 确保有足够的列
                    try:
                        # 解析每行数据
                        teacher_name = cells[1].get_text(strip=True)
                        teacher_total = int(cells[2].get_text(strip=True) or 0)
                        teacher_completed = int(cells[3].get_text(strip=True) or 0)
                        student_total = int(cells[4].get_text(strip=True) or 0)
                        student_completed = int(cells[5].get_text(strip=True) or 0)
                        completion_rate_text = cells[6].get_text(strip=True)

                        # 解析完成率
                        completion_rate = 0
                        if completion_rate_text and '%' in completion_rate_text:
                            completion_rate = float(completion_rate_text.replace('%', ''))

                        # 判断状态
                        status = 'completed' if completion_rate == 100 else 'incomplete'

                        teacher_detail = {
                            'name': teacher_name,
                            'username': '',  # 用户名需要从其他地方获取
                            'school': '',    # 学校信息需要从其他地方获取
                            'teacher_total': teacher_total,
                            'teacher_completed': teacher_completed,
                            'student_total': student_total,
                            'student_completed': student_completed,
                            'completion_rate': completion_rate,
                            'status': status
                        }

                        teacher_details.append(teacher_detail)

                        # 累计总数
                        completion_data['teacher_total'] += teacher_total
                        completion_data['teacher_completed'] += teacher_completed
                        completion_data['student_total'] += student_total
                        completion_data['student_completed'] += student_completed

                    except (ValueError, IndexError) as e:
                        logger.warning(f'解析表格行数据失败: {str(e)}')
                        continue

            # 计算总完成率
            if completion_data['teacher_total'] > 0:
                completion_data['teacher_completion_rate'] = round(
                    (completion_data['teacher_completed'] / completion_data['teacher_total']) * 100, 1
                )

            if completion_data['student_total'] > 0:
                completion_data['student_completion_rate'] = round(
                    (completion_data['student_completed'] / completion_data['student_total']) * 100, 1
                )

            completion_data['teacher_details'] = teacher_details

            logger.info(f'解析完成情况: 教师 {completion_data["teacher_completed"]}/{completion_data["teacher_total"]}, '
                       f'学生 {completion_data["student_completed"]}/{completion_data["student_total"]}')

        except Exception as e:
            logger.error(f'解析完成情况数据失败: {str(e)}')

        return completion_data

    def get_project_completion_status(self, special_id):
        """
        获取项目完成情况
        :param special_id: 项目ID
        :return: 完成情况统计
        """
        try:
            # 获取教师列表
            teacher_list = self.get_teacher_list()

            total_teachers = len(teacher_list)
            completed_teachers = []
            incomplete_teachers = []

            total_students = 0
            completed_students = 0

            for teacher_info in teacher_list:
                try:
                    # 创建教师实例检查完成情况
                    teacher = Teacher(teacher_info['username'], 'WCLhqxx2022')

                    try:
                        teacher.login()
                    except Exception:
                        # 登录失败，尝试重置密码
                        admin_info = self.get_user_info()
                        tmp_info = admin_info.copy()
                        resp = self.get_teacher_info(teacher_info)
                        tmp_info.update(resp)
                        teacher.change_password(tmp_info)
                        teacher.login()

                    # 检查教师专题课程完成情况
                    teacher_completed = self._check_teacher_special_completion(teacher, special_id)

                    # 获取学生列表并检查完成情况
                    student_list = teacher.get_student_list()
                    teacher_student_count = len([s for s in student_list if s.get('status') != '未激活'])
                    teacher_completed_students = 0

                    for student_info in student_list:
                        if student_info.get('status') == '未激活':
                            continue

                        total_students += 1

                        # 检查学生完成情况
                        if self._check_student_special_completion(student_info, special_id):
                            completed_students += 1
                            teacher_completed_students += 1

                    # 判断教师是否完成（教师本人完成 + 所有学生完成）
                    teacher_all_completed = teacher_completed and (teacher_completed_students == teacher_student_count)

                    teacher_status = {
                        'username': teacher_info['username'],
                        'nickname': teacher_info['nickname'],
                        'school': teacher_info.get('school', ''),
                        'teacher_completed': teacher_completed,
                        'student_count': teacher_student_count,
                        'completed_students': teacher_completed_students,
                        'completion_rate': round((teacher_completed_students / teacher_student_count * 100) if teacher_student_count > 0 else 100, 1),
                        'all_completed': teacher_all_completed
                    }

                    if teacher_all_completed:
                        completed_teachers.append(teacher_status)
                    else:
                        incomplete_teachers.append(teacher_status)

                except Exception as e:
                    logger.error(f'检查教师 {teacher_info.get("username", "未知")} 完成情况失败: {str(e)}')
                    # 将检查失败的教师标记为未完成
                    teacher_status = {
                        'username': teacher_info['username'],
                        'nickname': teacher_info['nickname'],
                        'school': teacher_info.get('school', ''),
                        'teacher_completed': False,
                        'student_count': 0,
                        'completed_students': 0,
                        'completion_rate': 0,
                        'all_completed': False,
                        'error': str(e)
                    }
                    incomplete_teachers.append(teacher_status)

            return {
                'special_id': special_id,
                'total_teachers': total_teachers,
                'completed_teachers_count': len(completed_teachers),
                'incomplete_teachers_count': len(incomplete_teachers),
                'teacher_completion_rate': round((len(completed_teachers) / total_teachers * 100) if total_teachers > 0 else 0, 1),
                'total_students': total_students,
                'completed_students_count': completed_students,
                'student_completion_rate': round((completed_students / total_students * 100) if total_students > 0 else 0, 1),
                'completed_teachers': completed_teachers,
                'incomplete_teachers': incomplete_teachers
            }

        except Exception as e:
            logger.error(f'获取项目 {special_id} 完成情况失败: {str(e)}')
            return {
                'special_id': special_id,
                'error': str(e),
                'total_teachers': 0,
                'completed_teachers_count': 0,
                'incomplete_teachers_count': 0,
                'teacher_completion_rate': 0,
                'total_students': 0,
                'completed_students_count': 0,
                'student_completion_rate': 0,
                'completed_teachers': [],
                'incomplete_teachers': []
            }

    def _check_teacher_special_completion(self, teacher, special_id):
        """检查教师专题课程完成情况"""
        try:
            # 这里需要根据实际的API来检查教师专题课程完成情况
            # 暂时返回False，需要根据实际情况实现
            return False
        except Exception:
            return False

    def _check_student_special_completion(self, student_info, special_id):
        """检查学生专题课程完成情况"""
        try:
            # 这里需要根据实际的API来检查学生专题课程完成情况
            # 暂时返回False，需要根据实际情况实现
            return False
        except Exception:
            return False

    def get_teacher_list(self):
        """
        获取教师账号等信息
        :return:
        """
        data_list = []
        url = "https://chongqing.xueanquan.com/eduadmin/TeacherManagement/TeacherManage"
        resp = self.session.post(url, data={"pageNum": 1, 'numPerPage': 100})
        soup = BeautifulSoup(resp.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            data_list.append({
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'class': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1]
            })
        return data_list

    def get_teacher_info(self, teacher_info):
        """
        获取教师年级，班级
        :return:
        """
        url = f'https://chongqing.xueanquan.com/eduadmin/TeacherManagement/TeacherEdit/{teacher_info["student_id"]}/{teacher_info["username"]}'
        resp = self.session.get(url)
        resp.encoding = 'utf-8'
        soup = BeautifulSoup(resp.text, 'lxml')
        return {
            'grade': soup.select_one('#TE_Grade1 > option[selected]').get('value'),
            'classRoom': soup.select_one('#combox_ManagementClassRoom1 > option[selected]').get('value'),
            'trueName': teacher_info['nickname'],
            'userName': teacher_info['username'],
            'UserType': 1,

        }
        pass

    def _get_student_count(self):
        """
        获取学生总数量
        :return:
        """
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement"
        resp = self.session.post(url, data={"pageNum": 1, 'numPerPage': 10})
        soup = BeautifulSoup(resp.text, 'lxml')
        return 1

    def get_student_by_class(self, class_id: str):
        """
        获取所有学生数据，根据第一页获取到总的数据条数，然后解析出所有数据，并yield
        :param class_id:
        :return:
        """
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement_School"
        # 将keywords参数进行url编码
        keywords = str(["", class_id, "", ""])
        data = f'keywords={keywords}&pageNum=1&numPerPage=100'
        response = self.session.post(url, data=data, headers={
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        })
        soup = BeautifulSoup(response.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            yield {
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'status': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1],
                'classRoom': class_id
            }

    def get_student_list(self, page=1):
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement"
        resp = self.session.post(url, data={"pageNum": page, 'numPerPage': 100})
        soup = BeautifulSoup(resp.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            yield {
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'classRoom': item.get('rel').split('/')[2],
                'status': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1],
            }


class SafetyEducationPlatform:
    """安全教育平台主控制类"""

    def __init__(self, config: Config):
        self.config = config

    def run(self) -> None:
        """运行主程序"""
        try:
            if self.config.role == 'admin':
                self._run_admin_mode()
            elif self.config.role == 'teacher':
                self._run_teacher_mode()
            elif self.config.role == 'student':
                self._run_student_mode()
            else:
                raise ValueError(f'不支持的角色类型: {self.config.role}')
        except Exception as e:
            logger.error(f'程序运行失败: {e}')
            raise

    def _run_admin_mode(self) -> None:
        """管理员模式"""
        logger.info('启动管理员模式')
        only_teacher = self._ask_only_teacher()
        admin_learning(self.config.username, self.config.password,
                      special_id='1178', only_teacher=only_teacher)

    def _run_teacher_mode(self) -> None:
        """教师模式"""
        logger.info('启动教师模式')
        teacher_learning(self.config.username, self.config.password)

    def _run_student_mode(self) -> None:
        """学生模式"""
        logger.info('启动学生模式')
        student = Student(self.config.username, self.config.password)
        student.login()
        student.complete_all_courses()

    def _ask_only_teacher(self) -> bool:
        """询问是否只处理教师"""
        try:
            choice = input('是否只学习教师？(y/n): ').strip().lower()
            return choice in ['y', 'yes', '是']
        except (EOFError, KeyboardInterrupt):
            logger.info('使用默认设置：处理所有用户')
            return False


def admin_learning(admin_name: str, admin_password: str,
                  special_id: Optional[str] = None, only_teacher: bool = False) -> None:
    """通过管理员账号进行批量学习"""
    try:
        admin = Admin(admin_name, admin_password)
        admin.login()
        admin_info = admin.get_user_info()
        teacher_list = admin.get_teacher_list()

        logger.info(f'找到 {len(teacher_list)} 个教师账号')

        for teacher_info in teacher_list:
            try:
                _process_teacher_account(admin, admin_info, teacher_info, special_id, only_teacher)
            except Exception as e:
                logger.error(f'处理教师 {teacher_info.get("username", "未知")} 失败: {e}')
                continue

    except Exception as e:
        logger.error(f'管理员学习模式失败: {e}')
        raise


def _process_teacher_account(admin: 'Admin', admin_info: Dict, teacher_info: Dict,
                           special_id: Optional[str], only_teacher: bool) -> None:
    """处理单个教师账号"""
    teacher = Teacher(teacher_info['username'], 'WCLhqxx2022')

    try:
        teacher.login()
    except Exception:
        # 登录失败，尝试重置密码
        tmp_info = admin_info.copy()
        resp = admin.get_teacher_info(teacher_info)
        tmp_info.update(resp)
        teacher.change_password(tmp_info)

    teacher_learning(teacher_info['username'], special_id=special_id, only_teacher=only_teacher)


def teacher_learning(teacher_name: str, teacher_password: str = 'WCLhqxx2022',
                    special_id: Optional[str] = None, only_teacher: bool = False) -> None:
    """通过教师账号进行学习"""
    try:
        teacher = Teacher(teacher_name, teacher_password)
        teacher.login()
        logger.success(f'教师 {teacher.nickname} 登录成功')

        # 完成教师课程
        _complete_teacher_courses(teacher, special_id)

        if only_teacher:
            return

        # 完成学生课程
        _complete_students_courses(teacher)

    except Exception as e:
        logger.error(f'教师学习模式失败: {e}')
        raise


def _complete_teacher_courses(teacher: 'Teacher', special_id: Optional[str]) -> None:
    """完成教师课程"""
    # 完成安全授课
    course_list = teacher.get_courses()
    if course_list:
        teacher.complete_skill_course(course_list[0])
    else:
        logger.info(f'{teacher.nickname}\t已完成所有安全课程')

    # 完成专题授课
    teacher.complete_special_course(special_id=special_id)


def _complete_students_courses(teacher: 'Teacher') -> None:
    """完成学生课程"""
    teacher_info = teacher.get_user_info()
    student_list = teacher.get_student_list()

    logger.info(f'找到 {len(student_list)} 个学生账号')

    for student_info in student_list:
        try:
            _process_student_account(teacher, teacher_info, student_info)
        except Exception as e:
            logger.error(f'处理学生 {student_info.get("username", "未知")} 失败: {e}')
            continue


def _process_student_account(teacher: 'Teacher', teacher_info: Dict, student_info: Dict) -> None:
    """处理单个学生账号"""
    # 跳过未激活账号
    if student_info.get('status') == '未激活':
        logger.warning(f"学生 {student_info['username']} 账号未激活")
        return

    student = Student(student_info['username'], 'WCLhqxx2022')

    try:
        student.login()
    except Exception:
        # 登录失败，尝试重置密码
        student.nickname = student_info['nickname']
        tmp_info = teacher_info.copy()
        tmp_info.update({
            'trueName': student_info['nickname'],
            'userName': student_info['username'],
        })
        student.change_password(tmp_info, 'WCLhqxx2022')
        student.login(password='WCLhqxx2022')

    # 开始学习
    student.complete_all_courses()


def main():
    """主函数"""
    try:
        config = Config.from_file('config.ini')
        platform = SafetyEducationPlatform(config)
        platform.run()
    except FileNotFoundError:
        logger.error('配置文件 config.ini 不存在')
    except Exception as e:
        logger.error(f'程序执行失败: {e}')


if __name__ == '__main__':
    main()
