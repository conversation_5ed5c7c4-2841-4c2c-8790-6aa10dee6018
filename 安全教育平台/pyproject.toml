[project]
name = "safety-education-platform-web"
version = "2.0.0"
description = "安全教育平台自动化学习工具 - Web界面版"
authors = [
    {name = "Safety Education Platform Team", email = "<EMAIL>"},
]
dependencies = [
    "requests>=2.25.0",
    "loguru>=0.5.0",
    "beautifulsoup4>=4.13.4",
    "urllib3>=1.26.0",
    "lxml>=4.6.0",
    "flask>=2.0.0",
    "flask-socketio>=5.0.0",
    "python-socketio>=5.0.0",
]
requires-python = ">=3.11"
readme = "README.md"
license = {text = "MIT"}
keywords = ["education", "automation", "safety", "web", "flask"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
    "Topic :: Internet :: WWW/HTTP :: WSGI :: Application",
]

[project.urls]
Homepage = "https://github.com/your-username/safety-education-platform"
Repository = "https://github.com/your-username/safety-education-platform.git"
Documentation = "https://github.com/your-username/safety-education-platform#readme"
"Bug Tracker" = "https://github.com/your-username/safety-education-platform/issues"

[project.scripts]
safety-edu = "index:main"
safety-edu-web = "start_web:main"

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "isort>=5.0",
    "flake8>=3.8",
    "mypy>=0.800",
]
test = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "pytest-mock>=3.0",
]

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[tool.pdm]
version = {source = "file", path = "safety_education/__init__.py"}
distribution = true

[tool.pdm.dev-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "isort>=5.0",
    "flake8>=3.8",
    "mypy>=0.800",
    "pre-commit>=2.0",
]

[tool.pdm.scripts]
# 启动命令行版本
start = "python index.py"
# 启动Web界面
web = "python start_web.py"
# 运行测试
test = "pytest tests/ -v"
# 代码格式化
format = "black . && isort ."
# 代码检查
lint = "flake8 . && mypy ."
# 安装开发依赖
install-dev = "pdm install -G dev"
# 构建项目
build = "pdm build"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["safety_education"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=safety_education",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["safety_education"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
