#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用Nuitka创建独立可执行文件版本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def check_nuitka():
    """检查Nuitka是否安装"""
    try:
        subprocess.run([sys.executable, '-m', 'nuitka', '--version'], capture_output=True, check=True)
        print("✅ Nuitka已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Nuitka未安装")
        print("📦 正在安装Nuitka...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka'], check=True)
            print("✅ Nuitka安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ Nuitka安装失败")
            return False

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', 'start_web.build', 'start_web.dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 已清理目录: {dir_name}")

def build_with_nuitka():
    """使用Nuitka构建独立可执行文件"""
    print("🔨 使用Nuitka构建独立可执行文件...")
    print("⚠️  首次编译可能需要下载编译器，请耐心等待...")

    # 简化的Nuitka编译命令
    cmd = [
        sys.executable, '-m', 'nuitka',
        '--standalone',
        '--onefile',
        '--output-filename=安全教育平台.exe',
        '--output-dir=dist',
        '--assume-yes-for-downloads',
        '--include-data-dir=templates=templates',
        'start_web.py'
    ]

    # 如果存在数据库文件，也包含进去
    if os.path.exists('data.db'):
        cmd.insert(-1, '--include-data-file=data.db=data.db')

    try:
        subprocess.run(cmd, check=True)
        print("✅ Nuitka构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka构建失败: {e}")
        print("💡 提示：首次使用Nuitka可能需要下载编译器")
        print("💡 如果失败，请确保网络连接正常并重试")
        return False

def create_nuitka_package():
    """创建Nuitka版本包"""
    print("📦 创建Nuitka版本包...")
    
    package_dir = Path('安全教育平台_Nuitka版')
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 复制可执行文件
    exe_file = Path('dist/安全教育平台.exe')
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir)
        print(f"✅ 已复制: {exe_file.name}")
    else:
        print("❌ 找不到可执行文件")
        return False
    
    return package_dir

def create_launchers(package_dir):
    """创建启动脚本"""
    print("🚀 创建启动脚本...")
    
    # 主启动脚本
    main_launcher = '''@echo off
chcp 65001 >nul
title 安全教育平台

echo.
echo ========================================
echo   安全教育平台 v2.0
echo   🚀 Nuitka编译版
echo ========================================
echo.

echo 🔍 检查程序文件...
if not exist "%~dp0安全教育平台.exe" (
    echo ❌ 错误：找不到程序文件
    echo 📁 请确保此脚本与程序文件在同一目录下
    pause
    exit /b 1
)
echo ✅ 程序文件检查通过

echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 程序启动后请稍等片刻
echo.

echo 🚀 正在启动程序...
cd /d "%~dp0"
start "安全教育平台" "%~dp0安全教育平台.exe"

echo.
echo ⏳ 等待程序启动...
timeout /t 5 /nobreak >nul

echo ✅ 程序已启动
echo 📱 请在浏览器中访问: http://localhost:5000
echo.
echo 💡 使用提示：
echo    • 如果浏览器没有自动打开，请手动访问上述地址
echo    • 程序在后台运行，关闭此窗口不会停止程序
echo    • 如需停止程序，请在任务管理器中结束进程
echo.
pause
'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='utf-8') as f:
        f.write(main_launcher)
    
    # 静默启动脚本
    silent_launcher = '''@echo off
cd /d "%~dp0"
start "" "%~dp0安全教育平台.exe"
'''
    
    with open(package_dir / '静默启动.bat', 'w', encoding='utf-8') as f:
        f.write(silent_launcher)
    
    # 调试启动脚本
    debug_launcher = '''@echo off
chcp 65001 >nul
title 安全教育平台 - 调试模式

echo.
echo ========================================
echo   安全教育平台 - 调试模式
echo ========================================
echo.

echo 🔧 调试模式启动...
echo 📋 此模式会显示详细的运行信息
echo.

cd /d "%~dp0"
"%~dp0安全教育平台.exe"

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
'''
    
    with open(package_dir / '调试启动.bat', 'w', encoding='utf-8') as f:
        f.write(debug_launcher)
    
    print("✅ 启动脚本已创建")

def create_readme(package_dir):
    """创建使用说明"""
    print("📖 创建使用说明...")
    
    readme_content = '''# 安全教育平台 v2.0 - Nuitka编译版

## 🚀 快速开始

### 推荐方式
1. 双击 `启动程序.bat`
2. 等待程序启动（首次启动可能需要几秒钟）
3. 在浏览器中访问 http://localhost:5000

### 其他方式
- `静默启动.bat` - 后台启动，不显示窗口
- `调试启动.bat` - 显示详细运行信息
- 直接双击 `安全教育平台.exe`

## 🌟 功能特点

- ✅ **Nuitka编译** - 使用先进的Nuitka编译器，性能更优
- ✅ **独立可执行** - 无需安装Python或任何依赖
- ✅ **Web可视化界面** - 实时显示学习进度
- ✅ **多角色支持** - 支持学生、教师、管理员模式
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **快速启动** - 编译后启动速度更快
- ✅ **绿色软件** - 解压即用，无需安装

## 🎯 使用步骤

1. **启动程序** - 双击启动脚本或可执行文件
2. **等待启动** - 首次启动可能需要几秒钟
3. **打开界面** - 在浏览器中访问 http://localhost:5000
4. **填写信息** - 输入用户名、密码，选择角色
5. **开始学习** - 点击"开始任务"按钮
6. **查看进度** - 实时查看学习进度和日志

## 🎭 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 使用 `调试启动.bat` 查看详细错误信息
   - 检查是否有杀毒软件拦截
   - 确保Windows系统版本兼容
   - 尝试以管理员身份运行

2. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查防火墙设置
   - 确认端口5000未被占用

3. **程序启动慢**
   - 首次启动需要初始化，请耐心等待
   - 后续启动会更快

4. **登录失败**
   - 检查账号密码是否正确
   - 确认网络连接正常
   - 检查是否频繁登录被限制

## 📋 系统要求

- **操作系统**: Windows 10/11
- **内存**: 至少 2GB RAM
- **网络**: 稳定的互联网连接
- **浏览器**: Chrome、Firefox、Edge等现代浏览器

## 🔒 安全说明

- 本程序使用Nuitka编译，代码已优化和保护
- 不会收集或泄露用户个人信息
- 仅用于自动化学习，请合理使用

## ⚡ Nuitka优势

- **更快的启动速度** - 编译后的程序启动更快
- **更好的性能** - 运行时性能优于解释执行
- **更强的保护** - 代码编译后更难被逆向
- **更小的体积** - 相比其他打包工具体积更小

---

**版本**: v2.0 Nuitka编译版  
**更新时间**: 2024年  
**兼容系统**: Windows 10/11  
**编译器**: Nuitka  
**特点**: 高性能，快速启动，无需Python环境
'''
    
    with open(package_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 使用说明已创建")

def create_zip_package(package_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    # 删除旧的压缩包
    if os.path.exists(zip_name):
        os.remove(zip_name)
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 压缩包已创建: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🎯 安全教育平台Nuitka版打包工具")
    print("=" * 50)
    
    # 检查Nuitka
    if not check_nuitka():
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 使用Nuitka构建
    if not build_with_nuitka():
        return False
    
    # 创建Nuitka版本包
    package_dir = create_nuitka_package()
    if not package_dir:
        return False
    
    # 创建启动脚本
    create_launchers(package_dir)
    
    # 创建使用说明
    create_readme(package_dir)
    
    # 创建ZIP压缩包
    zip_file = create_zip_package(package_dir)
    
    print("\n🎉 Nuitka版打包完成！")
    print(f"📁 Nuitka版目录: {package_dir}")
    print(f"📦 压缩包: {zip_file}")
    print(f"📏 压缩包大小: {os.path.getsize(zip_file) / 1024 / 1024:.1f} MB")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件到任意目录")
    print("2. 双击运行 '启动程序.bat' 或 '安全教育平台.exe'")
    print("3. 等待程序启动（首次可能需要几秒钟）")
    print("4. 在浏览器中使用Web界面")
    print("\n✨ Nuitka优势:")
    print("- ⚡ 启动速度更快")
    print("- 🚀 运行性能更好") 
    print("- 🔒 代码保护更强")
    print("- 💚 无需Python环境")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        sys.exit(1)
