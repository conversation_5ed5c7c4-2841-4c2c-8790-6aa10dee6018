@echo off
chcp 65001 >nul
title 安全教育平台Web版

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo ========================================
echo.

:menu
echo 请选择操作：
echo.
echo [1] 启动Web界面
echo [2] 安装/更新依赖
echo [3] 查看帮助
echo [0] 退出
echo.
set /p choice=请输入选项 (0-3):

if "%choice%"=="1" goto web
if "%choice%"=="2" goto install
if "%choice%"=="3" goto help
if "%choice%"=="0" goto exit
echo 无效选项，请重新选择
goto menu

:web
echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.
python start_web.py
pause
goto menu

:install
echo.
echo 📦 安装/更新依赖...
echo.
where pdm >nul 2>nul
if %errorlevel%==0 (
    echo 使用PDM安装依赖...
    pdm install
) else (
    echo PDM未安装，使用pip安装依赖...
    pip install -r requirements.txt
)
echo.
echo ✅ 依赖安装完成
pause
goto menu



:help
echo.
echo 📖 使用帮助
echo ========================================
echo.
echo 🌟 功能特点：
echo   • Web可视化界面，实时显示进度
echo   • 支持学生、教师、管理员三种角色
echo   • 自动完成所有学习任务
echo   • 完善的错误处理和重试机制
echo.
echo 📋 使用步骤：
echo   1. 选择选项1启动Web界面
echo   2. 在浏览器中填写账号信息
echo   3. 点击开始任务
echo.
echo 🔧 故障排除：
echo   • 如果依赖安装失败，请检查网络连接
echo   • 如果端口被占用，请关闭其他程序
echo   • 如果登录失败，请检查账号密码
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用安全教育平台Web版！
echo.
exit /b 0
