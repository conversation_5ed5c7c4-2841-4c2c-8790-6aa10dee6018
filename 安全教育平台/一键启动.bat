@echo off
chcp 65001 >nul
title 安全教育平台Web版 - 一键启动

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo   🚀 一键启动版
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python环境
    echo 📥 请先安装Python 3.8或更高版本
    echo 🌐 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo 📦 检查依赖包...
python -c "import flask, flask_socketio, requests, loguru, bs4" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  依赖包不完整，正在自动安装...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖包检查通过
)

echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.
echo ========================================
echo.

python start_web.py

echo.
echo 👋 程序已退出，感谢使用！
pause
