#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全教育平台Web版简单打包脚本
使用PyInstaller进行打包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        subprocess.run(['pyinstaller', '--version'], capture_output=True, check=True)
        print("✅ PyInstaller已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装")
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 已清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"🧹 已清理文件: {spec_file}")

def build_web_app():
    """使用PyInstaller编译Web应用"""
    print("🔨 开始编译Web应用...")
    
    # PyInstaller编译命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=安全教育平台_Web版',
        '--add-data=templates;templates',
        '--hidden-import=flask',
        '--hidden-import=flask_socketio',
        '--hidden-import=socketio',
        '--hidden-import=requests',
        '--hidden-import=loguru',
        '--hidden-import=bs4',
        '--hidden-import=lxml',
        '--hidden-import=urllib3',
        'start_web.py'
    ]
    
    # 如果存在数据库文件，也包含进去
    if os.path.exists('data.db'):
        cmd.insert(-1, '--add-data=data.db;.')
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Web应用编译成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Web应用编译失败: {e}")
        return False

def create_package_structure():
    """创建打包目录结构"""
    print("📁 创建打包目录结构...")
    
    package_dir = Path('安全教育平台_Web版_发布包')
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    return package_dir

def copy_files(package_dir):
    """复制必要文件到打包目录"""
    print("📋 复制文件...")
    
    # 复制可执行文件
    exe_file = Path('dist/安全教育平台_Web版.exe')
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir)
        print(f"✅ 已复制: {exe_file.name}")
    else:
        print("❌ 找不到可执行文件")
        return False
    
    # 复制一键启动脚本
    if os.path.exists('一键启动.bat'):
        shutil.copy2('一键启动.bat', package_dir)
        print("✅ 已复制: 一键启动.bat")
    
    return True

def create_launcher_script(package_dir):
    """创建启动脚本"""
    print("🚀 创建启动脚本...")
    
    launcher_content = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo   🔒 加密保护版
echo ========================================
echo.

echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.

start "" "安全教育平台_Web版.exe"

echo ✅ 程序已启动
echo 📱 请在浏览器中访问: http://localhost:5000
echo.
echo 💡 提示：关闭此窗口不会停止程序
echo    如需停止程序，请在程序窗口中按 Ctrl+C
echo.
pause
'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 启动脚本已创建")

def create_readme(package_dir):
    """创建使用说明"""
    print("📖 创建使用说明...")
    
    readme_content = '''# 安全教育平台Web版 v2.0 使用说明

## 🚀 快速开始

### 方法一：双击启动（推荐）
1. 双击 `启动程序.bat`
2. 等待程序启动
3. 浏览器会自动打开Web界面

### 方法二：直接运行
1. 双击 `安全教育平台_Web版.exe`
2. 手动打开浏览器访问 http://localhost:5000

## 🌟 功能特点

- ✅ **Web可视化界面** - 实时显示学习进度
- ✅ **多角色支持** - 支持学生、教师、管理员模式  
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **错误处理** - 完善的错误处理和重试机制
- ✅ **加密保护** - 核心代码已编译，防止逆向工程

## 🎯 使用步骤

1. **启动程序** - 双击启动脚本或可执行文件
2. **打开界面** - 在浏览器中访问 http://localhost:5000
3. **填写信息** - 输入用户名、密码，选择角色
4. **开始学习** - 点击"开始任务"按钮
5. **查看进度** - 实时查看学习进度和日志

## 🎭 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)  
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查是否有杀毒软件拦截
   - 确保Windows系统版本兼容（支持Win10/11）
   - 尝试以管理员身份运行

2. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查防火墙设置
   - 确认端口5000未被占用

3. **登录失败**
   - 检查账号密码是否正确
   - 确认网络连接正常
   - 检查是否频繁登录被限制

4. **学习失败**
   - 检查网络连接
   - 确认账号状态正常
   - 查看日志中的具体错误信息

### 技术支持

如遇到其他问题，请：
- 检查程序运行日志
- 确认网络环境正常
- 验证账号信息正确

## 📋 系统要求

- **操作系统**: Windows 10/11
- **内存**: 至少 2GB RAM
- **网络**: 稳定的互联网连接
- **浏览器**: Chrome、Firefox、Edge等现代浏览器

## 🔒 安全说明

- 本程序已进行代码编译和加密处理
- 不会收集或泄露用户个人信息
- 仅用于自动化学习，请合理使用

---

**版本**: v2.0  
**更新时间**: 2024年  
**兼容系统**: Windows 10/11
'''
    
    with open(package_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 使用说明已创建")

def create_zip_package(package_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 压缩包已创建: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🚀 安全教育平台Web版简单打包工具")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 编译Web应用
    if not build_web_app():
        return False
    
    # 创建打包目录结构
    package_dir = create_package_structure()
    
    # 复制文件
    if not copy_files(package_dir):
        return False
    
    # 创建启动脚本
    create_launcher_script(package_dir)
    
    # 创建使用说明
    create_readme(package_dir)
    
    # 创建ZIP压缩包
    zip_file = create_zip_package(package_dir)
    
    print("\n🎉 打包完成！")
    print(f"📁 打包目录: {package_dir}")
    print(f"📦 压缩包: {zip_file}")
    print(f"📏 压缩包大小: {os.path.getsize(zip_file) / 1024 / 1024:.1f} MB")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件到任意目录")
    print("2. 双击运行 '启动程序.bat'")
    print("3. 在浏览器中使用Web界面")
    print("\n✨ 特点:")
    print("- 🔒 核心代码已加密编译")
    print("- 📦 单文件可执行，无需安装Python")
    print("- 🌐 Web界面，操作简单直观")
    print("- ⚡ 一键启动，开箱即用")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        sys.exit(1)
