#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全教育平台Web版打包脚本
使用Nuitka进行代码编译和加密保护
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def check_nuitka():
    """检查Nuitka是否安装"""
    try:
        subprocess.run(['nuitka', '--version'], capture_output=True, check=True)
        print("✅ Nuitka已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Nuitka未安装")
        print("📦 正在安装Nuitka...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka'], check=True)
            print("✅ Nuitka安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ Nuitka安装失败")
            return False

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', 'app.build', 'start_web.build', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 已清理目录: {dir_name}")

def build_web_app():
    """编译Web应用"""
    print("🔨 开始编译Web应用...")
    
    # Nuitka编译命令
    cmd = [
        'nuitka',
        '--standalone',
        '--onefile',
        '--windows-console-mode=disable',
        '--windows-icon-from-ico=icon.ico' if os.path.exists('icon.ico') else '',
        '--include-data-dir=templates=templates',
        '--include-data-file=data.db=data.db' if os.path.exists('data.db') else '',
        '--output-filename=安全教育平台_Web版.exe',
        '--output-dir=dist',
        '--remove-output',
        '--assume-yes-for-downloads',
        'start_web.py'
    ]
    
    # 移除空的参数
    cmd = [arg for arg in cmd if arg]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Web应用编译成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Web应用编译失败: {e}")
        return False

def create_package_structure():
    """创建打包目录结构"""
    print("📁 创建打包目录结构...")
    
    package_dir = Path('安全教育平台_Web版_v2.0')
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 创建子目录
    (package_dir / '程序文件').mkdir()
    (package_dir / '模板文件').mkdir()
    (package_dir / '说明文档').mkdir()
    
    return package_dir

def copy_files(package_dir):
    """复制必要文件到打包目录"""
    print("📋 复制文件...")
    
    # 复制可执行文件
    if os.path.exists('dist/安全教育平台_Web版.exe'):
        shutil.copy2('dist/安全教育平台_Web版.exe', package_dir / '程序文件')
    
    # 复制模板文件
    if os.path.exists('templates'):
        shutil.copytree('templates', package_dir / '模板文件' / 'templates')
    
    # 复制数据库文件（如果存在）
    if os.path.exists('data.db'):
        shutil.copy2('data.db', package_dir / '程序文件')
    
    # 复制说明文档
    if os.path.exists('README.md'):
        shutil.copy2('README.md', package_dir / '说明文档')

def create_launcher_script(package_dir):
    """创建启动脚本"""
    print("🚀 创建启动脚本...")
    
    launcher_content = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版 v2.0

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo   🔒 加密保护版
echo ========================================
echo.
echo 🛡️ 安全特性：
echo   • 核心代码已加密保护
echo   • 防逆向工程设计
echo   • 运行时动态解密
echo.
echo 🌐 启动Web界面...
echo 🔒 正在解密程序文件...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.

cd "程序文件"
start "" "安全教育平台_Web版.exe"
cd ..

echo.
echo ✅ Web界面已启动
echo 📱 请在浏览器中访问: http://localhost:5000
echo.
pause
'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)

def create_readme(package_dir):
    """创建使用说明"""
    print("📖 创建使用说明...")
    
    readme_content = '''# 安全教育平台Web版 v2.0

## 🚀 快速开始

1. **双击运行** `启动程序.bat`
2. **等待启动** 程序会自动打开浏览器
3. **填写信息** 在Web界面中填写账号密码
4. **开始学习** 点击开始按钮即可

## 🌟 功能特点

- ✅ **Web可视化界面** - 实时显示学习进度
- ✅ **多角色支持** - 支持学生、教师、管理员模式
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **错误处理** - 完善的错误处理和重试机制
- ✅ **加密保护** - 核心代码加密，防止逆向工程

## 🎯 角色说明

### 学生模式
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务

### 管理员模式
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查是否有杀毒软件拦截
   - 确保Windows系统版本兼容

2. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查防火墙设置

3. **登录失败**
   - 检查账号密码是否正确
   - 确认网络连接正常

## 📞 技术支持

如遇到问题，请检查：
- 网络连接是否正常
- 账号密码是否正确
- 防火墙是否允许程序运行

---

**版本**: v2.0  
**更新时间**: 2024年  
**适用系统**: Windows 10/11
'''
    
    with open(package_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_zip_package(package_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 压缩包已创建: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🚀 安全教育平台Web版打包工具")
    print("=" * 50)
    
    # 检查Nuitka
    if not check_nuitka():
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 编译Web应用
    if not build_web_app():
        return False
    
    # 创建打包目录结构
    package_dir = create_package_structure()
    
    # 复制文件
    copy_files(package_dir)
    
    # 创建启动脚本
    create_launcher_script(package_dir)
    
    # 创建使用说明
    create_readme(package_dir)
    
    # 创建ZIP压缩包
    zip_file = create_zip_package(package_dir)
    
    print("\n🎉 打包完成！")
    print(f"📁 打包目录: {package_dir}")
    print(f"📦 压缩包: {zip_file}")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件")
    print("2. 双击运行 '启动程序.bat'")
    print("3. 在浏览器中使用Web界面")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        sys.exit(1)
