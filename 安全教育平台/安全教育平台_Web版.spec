# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_web.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('data.db', '.') if os.path.exists('data.db') else None,
    ],
    hiddenimports=[
        'flask',
        'flask_socketio',
        'socketio',
        'requests',
        'loguru',
        'bs4',
        'beautifulsoup4',
        'lxml',
        'urllib3',
        'sqlite3',
        'threading',
        'webbrowser',
        'time',
        'pathlib',
        'configparser',
        'datetime',
        'json',
        're',
        'random',
        'functools',
        'dataclasses',
        'typing',
        'app',
        'index',
        'database',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉None值
a.datas = [x for x in a.datas if x is not None]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='安全教育平台_Web版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
