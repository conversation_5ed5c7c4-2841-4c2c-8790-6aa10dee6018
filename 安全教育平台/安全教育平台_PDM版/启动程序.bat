@echo off
chcp 65001 >nul
title 安全教育平台Web版

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python环境
    echo 📥 请先安装Python 3.8或更高版本
    echo 🌐 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo 📦 检查PDM...
pdm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PDM未安装，正在自动安装...
    pip install pdm
    if %errorlevel% neq 0 (
        echo ❌ PDM安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ PDM安装完成
) else (
    echo ✅ PDM检查通过
)

echo.
echo 📦 安装项目依赖...
pdm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.

pdm run web

echo.
echo 👋 程序已退出，感谢使用！
pause
