# 安全教育平台Web版 v2.0 - PDM版

## 🚀 快速开始

### 首次使用
1. 双击运行 `启动程序.bat`
2. 等待自动安装PDM和依赖包
3. 浏览器会自动打开Web界面

### 后续使用
1. 双击运行 `快速启动.bat`
2. 直接启动程序（跳过依赖检查）

### 手动安装依赖
1. 双击运行 `安装依赖.bat`
2. 等待依赖安装完成

## 🌟 功能特点

- ✅ **Web可视化界面** - 实时显示学习进度
- ✅ **多角色支持** - 支持学生、教师、管理员模式
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **PDM管理** - 使用现代化的PDM进行依赖管理
- ✅ **源码开放** - 可以查看和修改源代码

## 🎯 使用步骤

1. **启动程序** - 双击启动脚本
2. **打开界面** - 在浏览器中访问 http://localhost:5000
3. **填写信息** - 输入用户名、密码，选择角色
4. **开始学习** - 点击"开始任务"按钮
5. **查看进度** - 实时查看学习进度和日志

## 🎭 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生

## 📁 文件说明

- `启动程序.bat` - 完整启动脚本（包含依赖检查和安装）
- `快速启动.bat` - 快速启动脚本（跳过依赖检查）
- `安装依赖.bat` - 单独安装依赖脚本
- `app.py` - Web应用主文件
- `start_web.py` - Web启动脚本
- `index.py` - 核心业务逻辑
- `pyproject.toml` - PDM项目配置文件
- `templates/` - Web界面模板

## 🔧 故障排除

### 常见问题

1. **PDM安装失败**
   - 检查网络连接
   - 尝试手动安装：`pip install pdm`

2. **依赖安装失败**
   - 检查网络连接
   - 尝试切换pip源：`pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple`

3. **程序无法启动**
   - 确保Python 3.8+已安装
   - 检查防火墙设置

4. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查端口5000是否被占用

## 📋 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8或更高版本
- **内存**: 至少 2GB RAM
- **网络**: 稳定的互联网连接（用于安装依赖）

## 🛠️ 开发说明

### PDM命令

```bash
# 安装依赖
pdm install

# 启动Web界面
pdm run web

# 查看所有可用命令
pdm run --list
```

### 修改代码

所有源代码都可以直接查看和修改：
- 修改Web界面：编辑 `templates/index_new.html`
- 修改业务逻辑：编辑 `index.py`
- 修改Web应用：编辑 `app.py`

---

**版本**: v2.0 PDM版  
**更新时间**: 2024年  
**适用系统**: Windows 10/11  
**依赖管理**: PDM
