@echo off
chcp 65001 >nul
title 安装依赖

echo.
echo ========================================
echo   安装项目依赖
echo ========================================
echo.

echo 📦 检查PDM...
pdm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PDM未安装，正在自动安装...
    pip install pdm
    if %errorlevel% neq 0 (
        echo ❌ PDM安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ PDM安装完成
)

echo.
echo 📦 安装项目依赖...
pdm install

echo.
echo ✅ 依赖安装完成！
echo 💡 现在可以使用 "快速启动.bat" 来启动程序
pause
