import sqlite3
import json
from datetime import datetime
from loguru import logger
import os

class Database:
    def __init__(self, db_path='data.db'):
        self.db_path = db_path
        self.init_database()
        self.migrate_database()
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'student',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 教师表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS teachers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                nickname TEXT,
                school TEXT,
                class_count INTEGER DEFAULT 0,
                student_count INTEGER DEFAULT 0,
                admin_username TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT NOT NULL UNIQUE,
                value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 任务表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_type TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                target_users TEXT,
                config TEXT,
                result TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 项目表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                special_id TEXT,
                url TEXT,
                status TEXT DEFAULT 'active',
                admin_username TEXT,
                teacher_total INTEGER DEFAULT 0,
                teacher_completed INTEGER DEFAULT 0,
                teacher_completion_rate REAL DEFAULT 0,
                student_total INTEGER DEFAULT 0,
                student_completed INTEGER DEFAULT 0,
                student_completion_rate REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 项目教师完成详情表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS project_teacher_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                special_id TEXT,
                teacher_username TEXT,
                teacher_name TEXT,
                school TEXT,
                teacher_total INTEGER DEFAULT 0,
                teacher_completed INTEGER DEFAULT 0,
                student_total INTEGER DEFAULT 0,
                student_completed INTEGER DEFAULT 0,
                completion_rate REAL DEFAULT 0,
                status TEXT DEFAULT 'incomplete',
                admin_username TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )
        ''')
        
        conn.commit()
        conn.close()

    def migrate_database(self):
        """数据库迁移，添加缺失的字段"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 检查projects表是否存在新字段
            cursor.execute("PRAGMA table_info(projects)")
            columns = [column[1] for column in cursor.fetchall()]

            # 需要添加的字段
            new_columns = [
                ('teacher_total', 'INTEGER DEFAULT 0'),
                ('teacher_completed', 'INTEGER DEFAULT 0'),
                ('teacher_completion_rate', 'REAL DEFAULT 0'),
                ('student_total', 'INTEGER DEFAULT 0'),
                ('student_completed', 'INTEGER DEFAULT 0'),
                ('student_completion_rate', 'REAL DEFAULT 0')
            ]

            # 添加缺失的字段
            for column_name, column_def in new_columns:
                if column_name not in columns:
                    try:
                        cursor.execute(f'ALTER TABLE projects ADD COLUMN {column_name} {column_def}')
                        logger.info(f'添加字段 {column_name} 到 projects 表')
                    except Exception as e:
                        logger.warning(f'添加字段 {column_name} 失败: {str(e)}')

            conn.commit()

        except Exception as e:
            logger.error(f'数据库迁移失败: {str(e)}')
            conn.rollback()
        finally:
            conn.close()

    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)
    
    # 用户相关操作
    def add_user(self, name, username, password, role='student'):
        """添加用户"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO users (name, username, password, role)
                VALUES (?, ?, ?, ?)
            ''', (name, username, password, role))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()
    
    def get_users(self):
        """获取所有用户"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM users ORDER BY created_at DESC')
        users = cursor.fetchall()
        conn.close()
        
        return [
            {
                'id': user[0],
                'name': user[1],
                'username': user[2],
                'password': user[3],
                'role': user[4],
                'created_at': user[5],
                'updated_at': user[6]
            }
            for user in users
        ]
    
    def update_user(self, user_id, name, username, password, role):
        """更新用户"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                UPDATE users 
                SET name=?, username=?, password=?, role=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            ''', (name, username, password, role, user_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def delete_user(self, user_id):
        """删除用户"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM users WHERE id=?', (user_id,))
        conn.commit()
        result = cursor.rowcount > 0
        conn.close()
        return result
    
    # 教师相关操作
    def save_teachers(self, teachers, admin_username):
        """保存教师列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 清空现有教师数据
        cursor.execute('DELETE FROM teachers WHERE admin_username=?', (admin_username,))
        
        # 插入新的教师数据
        for teacher in teachers:
            cursor.execute('''
                INSERT INTO teachers (username, nickname, school, class_count, student_count, admin_username)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                teacher.get('username', ''),
                teacher.get('nickname', ''),
                teacher.get('school', ''),
                teacher.get('class_count', 0),
                teacher.get('student_count', 0),
                admin_username
            ))
        
        conn.commit()
        conn.close()
    
    def get_teachers(self, admin_username=None):
        """获取教师列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if admin_username:
            cursor.execute('SELECT * FROM teachers WHERE admin_username=? ORDER BY created_at DESC', (admin_username,))
        else:
            cursor.execute('SELECT * FROM teachers ORDER BY created_at DESC')
        
        teachers = cursor.fetchall()
        conn.close()
        
        return [
            {
                'id': teacher[0],
                'username': teacher[1],
                'nickname': teacher[2],
                'school': teacher[3],
                'class_count': teacher[4],
                'student_count': teacher[5],
                'admin_username': teacher[6],
                'created_at': teacher[7],
                'updated_at': teacher[8]
            }
            for teacher in teachers
        ]
    
    def clear_teachers(self, admin_username=None):
        """清空教师列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if admin_username:
            cursor.execute('DELETE FROM teachers WHERE admin_username=?', (admin_username,))
        else:
            cursor.execute('DELETE FROM teachers')
        
        conn.commit()
        result = cursor.rowcount
        conn.close()
        return result

    # 项目相关操作
    def save_projects(self, projects, admin_username=None):
        """保存项目列表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 清空现有项目数据
        cursor.execute('DELETE FROM projects')

        # 插入新的项目数据
        for project in projects:
            cursor.execute('''
                INSERT INTO projects (title, special_id, url, status, admin_username,
                                    teacher_total, teacher_completed, teacher_completion_rate,
                                    student_total, student_completed, student_completion_rate)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                project.get('title', ''),
                project.get('special_id', ''),
                project.get('url', ''),
                project.get('status', 'active'),
                admin_username or 'system',  # 使用system作为默认值
                project.get('teacher_total', 0),
                project.get('teacher_completed', 0),
                project.get('teacher_completion_rate', 0),
                project.get('student_total', 0),
                project.get('student_completed', 0),
                project.get('student_completion_rate', 0)
            ))

        conn.commit()
        conn.close()

    def get_projects(self, admin_username=None):
        """获取项目列表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 获取所有项目，不区分管理员
        cursor.execute('SELECT * FROM projects ORDER BY created_at DESC')

        projects = cursor.fetchall()
        conn.close()

        return [
            {
                'id': project[0],
                'title': project[1],
                'special_id': project[2],
                'url': project[3],
                'status': project[4],
                'created_at': project[12],
                'updated_at': project[13]
            }
            for project in projects
        ]

    def clear_projects(self, admin_username=None):
        """清空项目列表"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 清空所有项目
        cursor.execute('DELETE FROM projects')

        conn.commit()
        result = cursor.rowcount
        conn.close()
        return result

    def update_project_completion(self, special_id, admin_username, completion_data):
        """更新项目完成情况"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # 更新项目表的完成情况
            cursor.execute('''
                UPDATE projects
                SET teacher_total=?, teacher_completed=?, teacher_completion_rate=?,
                    student_total=?, student_completed=?, student_completion_rate=?,
                    updated_at=CURRENT_TIMESTAMP
                WHERE special_id=? AND admin_username=?
            ''', (
                completion_data.get('teacher_total', 0),
                completion_data.get('teacher_completed', 0),
                completion_data.get('teacher_completion_rate', 0),
                completion_data.get('student_total', 0),
                completion_data.get('student_completed', 0),
                completion_data.get('student_completion_rate', 0),
                special_id,
                admin_username
            ))

            # 获取项目ID
            cursor.execute('SELECT id FROM projects WHERE special_id=? AND admin_username=?', (special_id, admin_username))
            project_result = cursor.fetchone()

            if project_result:
                project_id = project_result[0]

                # 清空现有的教师详情数据
                cursor.execute('DELETE FROM project_teacher_details WHERE project_id=?', (project_id,))

                # 插入新的教师详情数据
                teacher_details = completion_data.get('teacher_details', [])
                for teacher in teacher_details:
                    cursor.execute('''
                        INSERT INTO project_teacher_details
                        (project_id, special_id, teacher_username, teacher_name, school,
                         teacher_total, teacher_completed, student_total, student_completed,
                         completion_rate, status, admin_username)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        project_id,
                        special_id,
                        teacher.get('username', ''),
                        teacher.get('name', ''),
                        teacher.get('school', ''),
                        teacher.get('teacher_total', 0),
                        teacher.get('teacher_completed', 0),
                        teacher.get('student_total', 0),
                        teacher.get('student_completed', 0),
                        teacher.get('completion_rate', 0),
                        teacher.get('status', 'incomplete'),
                        admin_username
                    ))

            conn.commit()
            return True

        except Exception as e:
            conn.rollback()
            logger.error(f'更新项目完成情况失败: {str(e)}')
            return False
        finally:
            conn.close()

    def get_project_completion_details(self, special_id, admin_username):
        """获取项目完成详情"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 获取项目基本信息
        cursor.execute('''
            SELECT teacher_total, teacher_completed, teacher_completion_rate,
                   student_total, student_completed, student_completion_rate
            FROM projects
            WHERE special_id=? AND admin_username=?
        ''', (special_id, admin_username))

        project_result = cursor.fetchone()

        if not project_result:
            conn.close()
            return None

        # 获取教师详情
        cursor.execute('''
            SELECT teacher_username, teacher_name, school, teacher_total, teacher_completed,
                   student_total, student_completed, completion_rate, status
            FROM project_teacher_details
            WHERE special_id=? AND admin_username=?
            ORDER BY completion_rate ASC, teacher_name
        ''', (special_id, admin_username))

        teacher_details = cursor.fetchall()
        conn.close()

        return {
            'teacher_total': project_result[0],
            'teacher_completed': project_result[1],
            'teacher_completion_rate': project_result[2],
            'student_total': project_result[3],
            'student_completed': project_result[4],
            'student_completion_rate': project_result[5],
            'teacher_details': [
                {
                    'username': detail[0],
                    'name': detail[1],
                    'school': detail[2],
                    'teacher_total': detail[3],
                    'teacher_completed': detail[4],
                    'student_total': detail[5],
                    'student_completed': detail[6],
                    'completion_rate': detail[7],
                    'status': detail[8]
                }
                for detail in teacher_details
            ]
        }

    # 配置相关操作
    def set_config(self, key, value):
        """设置配置"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO config (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        ''', (key, json.dumps(value) if isinstance(value, (dict, list)) else str(value)))
        conn.commit()
        conn.close()
    
    def get_config(self, key, default=None):
        """获取配置"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT value FROM config WHERE key=?', (key,))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            try:
                return json.loads(result[0])
            except:
                return result[0]
        return default
    
    def get_all_config(self):
        """获取所有配置"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT key, value FROM config')
        configs = cursor.fetchall()
        conn.close()
        
        result = {}
        for key, value in configs:
            try:
                result[key] = json.loads(value)
            except:
                result[key] = value
        return result
    
    # 日志相关操作
    def add_log(self, level, message):
        """添加日志"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO logs (level, message)
            VALUES (?, ?)
        ''', (level, message))
        conn.commit()
        conn.close()
    
    def get_logs(self, limit=100):
        """获取日志"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM logs ORDER BY timestamp DESC LIMIT ?', (limit,))
        logs = cursor.fetchall()
        conn.close()
        
        return [
            {
                'id': log[0],
                'level': log[1],
                'message': log[2],
                'timestamp': log[3]
            }
            for log in logs
        ]
    
    # 任务相关操作
    def create_task(self, task_type, target_users, config=None):
        """创建任务"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO tasks (task_type, target_users, config)
            VALUES (?, ?, ?)
        ''', (task_type, json.dumps(target_users), json.dumps(config) if config else None))
        conn.commit()
        task_id = cursor.lastrowid
        conn.close()
        return task_id
    
    def update_task_status(self, task_id, status, result=None):
        """更新任务状态"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE tasks 
            SET status=?, result=?, updated_at=CURRENT_TIMESTAMP
            WHERE id=?
        ''', (status, json.dumps(result) if result else None, task_id))
        conn.commit()
        conn.close()
    
    def get_tasks(self, limit=50):
        """获取任务列表"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM tasks ORDER BY created_at DESC LIMIT ?', (limit,))
        tasks = cursor.fetchall()
        conn.close()

        return [
            {
                'id': task[0],
                'task_type': task[1],
                'status': task[2],
                'target_users': json.loads(task[3]) if task[3] else [],
                'config': json.loads(task[4]) if task[4] else {},
                'result': json.loads(task[5]) if task[5] else None,
                'created_at': task[6],
                'updated_at': task[7]
            }
            for task in tasks
        ]

    # 任务执行记录相关操作（兼容方法）
    def create_task_execution(self, task_type, target_users, total=0):
        """创建任务执行记录"""
        return self.create_task(task_type, target_users, {'total': total})

    def update_task_execution(self, task_id, status=None, progress=None, error_message=None):
        """更新任务执行状态"""
        if status:
            result = {}
            if progress is not None:
                result['progress'] = progress
            if error_message:
                result['error_message'] = error_message
            self.update_task_status(task_id, status, result)

    def get_task_execution(self, task_id):
        """获取任务执行记录"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM tasks WHERE id=?', (task_id,))
        task = cursor.fetchone()
        conn.close()

        if task:
            return {
                'id': task[0],
                'task_type': task[1],
                'status': task[2],
                'target_users': json.loads(task[3]) if task[3] else [],
                'config': json.loads(task[4]) if task[4] else {},
                'result': json.loads(task[5]) if task[5] else None,
                'created_at': task[6],
                'updated_at': task[7]
            }
        return None

# 全局数据库实例
db = Database()
