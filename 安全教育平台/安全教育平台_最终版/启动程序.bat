@echo off
chcp 65001 >nul
title 安全教育平台

echo.
echo ========================================
echo   安全教育平台 v2.0
echo ========================================
echo.

echo 🔍 检查程序文件...
if not exist "%~dp0安全教育平台.exe" (
    echo ❌ 错误：找不到程序文件
    echo 📁 请确保此脚本与程序文件在同一目录下
    pause
    exit /b 1
)
echo ✅ 程序文件检查通过

echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 程序启动后请稍等片刻
echo.

echo 🚀 正在启动程序...
cd /d "%~dp0"
"%~dp0安全教育平台.exe"

echo.
echo 程序已退出，按任意键关闭窗口...
pause
