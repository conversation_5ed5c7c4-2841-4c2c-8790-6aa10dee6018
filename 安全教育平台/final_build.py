#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终完整打包脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def build_complete_exe():
    """构建完整的可执行文件"""
    print("🔨 构建完整的可执行文件...")
    
    # 完整的PyInstaller命令，包含所有必要的模块
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',
        '--name=安全教育平台_完整版',
        '--add-data=templates;templates',
        '--add-data=app.py;.',
        '--add-data=index.py;.',
        '--add-data=database.py;.',
        '--hidden-import=json',
        '--hidden-import=os',
        '--hidden-import=sys',
        '--hidden-import=threading',
        '--hidden-import=time',
        '--hidden-import=datetime',
        '--hidden-import=pathlib',
        '--hidden-import=configparser',
        '--hidden-import=re',
        '--hidden-import=random',
        '--hidden-import=functools',
        '--hidden-import=dataclasses',
        '--hidden-import=typing',
        '--hidden-import=sqlite3',
        '--hidden-import=webbrowser',
        '--hidden-import=flask',
        '--hidden-import=flask_socketio',
        '--hidden-import=socketio',
        '--hidden-import=requests',
        '--hidden-import=loguru',
        '--hidden-import=bs4',
        '--hidden-import=beautifulsoup4',
        '--hidden-import=lxml',
        '--hidden-import=urllib3',
        '--hidden-import=app',
        '--hidden-import=index',
        '--hidden-import=database',
        '--collect-all=flask',
        '--collect-all=flask_socketio',
        '--collect-all=bs4',
        '--collect-all=lxml',
        'start_web.py'
    ]
    
    # 如果存在数据库文件，也包含进去
    if os.path.exists('data.db'):
        cmd.insert(-1, '--add-data=data.db;.')
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 完整版构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 完整版构建失败: {e}")
        return False

def create_final_package():
    """创建最终发布包"""
    print("📦 创建最终发布包...")
    
    package_dir = Path('安全教育平台_最终发布版')
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 复制可执行文件
    exe_file = Path('dist/安全教育平台_完整版.exe')
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir / '安全教育平台.exe')
        print(f"✅ 已复制: 安全教育平台.exe")
    else:
        print("❌ 找不到可执行文件")
        return False
    
    return package_dir

def create_final_launchers(package_dir):
    """创建最终启动脚本"""
    print("🚀 创建最终启动脚本...")
    
    # 主启动脚本 - 简化版
    main_launcher = '''@echo off
echo Starting Safety Education Platform...
echo Please visit: http://localhost:5000
cd /d "%~dp0"
"安全教育平台.exe"
echo.
echo Program finished. Press any key to close...
pause'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='gbk') as f:
        f.write(main_launcher)
    
    # 后台启动脚本
    background_launcher = '''@echo off
cd /d "%~dp0"
start "" "安全教育平台.exe"
echo Program started in background.
echo Please visit: http://localhost:5000'''
    
    with open(package_dir / '后台启动.bat', 'w', encoding='gbk') as f:
        f.write(background_launcher)
    
    print("✅ 启动脚本已创建")

def create_final_readme(package_dir):
    """创建最终使用说明"""
    print("📖 创建最终使用说明...")
    
    readme_content = '''# 安全教育平台 v2.0 - 最终版

## 快速开始

1. 双击 "启动程序.bat" 或 "安全教育平台.exe"
2. 等待程序启动
3. 在浏览器中访问 http://localhost:5000

## 其他启动方式

- "后台启动.bat" - 后台运行，不显示控制台
- 直接双击 "安全教育平台.exe"

## 功能特点

- 无需安装Python环境
- Web可视化界面
- 支持学生、教师、管理员模式
- 自动化学习功能
- 实时进度显示

## 角色说明

### 学生模式
自动完成当前学生的所有课程

### 教师模式  
完成教师授课任务，处理班级学生学习

### 管理员模式
批量处理所有教师账号

## 故障排除

1. 程序无法启动 - 检查杀毒软件是否拦截
2. 浏览器无法打开 - 手动访问 http://localhost:5000
3. 端口被占用 - 关闭其他占用5000端口的程序

## 系统要求

- Windows 10/11
- 至少 2GB RAM
- 稳定的网络连接

---
版本: v2.0 最终版
更新: 2024年
'''
    
    with open(package_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 使用说明已创建")

def create_final_zip(package_dir):
    """创建最终ZIP包"""
    print("📦 创建最终ZIP包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    if os.path.exists(zip_name):
        os.remove(zip_name)
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 最终ZIP包已创建: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🎯 安全教育平台最终完整打包工具")
    print("=" * 50)
    
    # 清理旧文件
    dirs_to_clean = ['build', 'dist']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 已清理: {dir_name}")
    
    # 构建完整版可执行文件
    if not build_complete_exe():
        return False
    
    # 创建最终发布包
    package_dir = create_final_package()
    if not package_dir:
        return False
    
    # 创建启动脚本
    create_final_launchers(package_dir)
    
    # 创建使用说明
    create_final_readme(package_dir)
    
    # 创建ZIP包
    zip_file = create_final_zip(package_dir)
    
    print("\n🎉 最终版打包完成！")
    print(f"📁 发布包目录: {package_dir}")
    print(f"📦 ZIP包: {zip_file}")
    print(f"📏 ZIP包大小: {os.path.getsize(zip_file) / 1024 / 1024:.1f} MB")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件")
    print("2. 双击运行 '启动程序.bat'")
    print("3. 在浏览器中使用")
    print("\n✨ 特点:")
    print("- 🚀 包含所有必要模块")
    print("- 💻 控制台模式，便于调试")
    print("- 📦 单文件可执行")
    print("- 🌐 Web界面操作")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        sys.exit(1)
