#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终打包脚本 - 解决依赖问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 已清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"🧹 已清理文件: {spec_file}")

def create_spec_file():
    """创建自定义的spec文件"""
    print("📝 创建自定义spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_web.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('data.db', '.') if os.path.exists('data.db') else None,
    ],
    hiddenimports=[
        'flask',
        'flask_socketio',
        'socketio',
        'requests',
        'loguru',
        'bs4',
        'beautifulsoup4',
        'lxml',
        'urllib3',
        'sqlite3',
        'threading',
        'webbrowser',
        'time',
        'pathlib',
        'configparser',
        'datetime',
        'json',
        're',
        'random',
        'functools',
        'dataclasses',
        'typing',
        'app',
        'index',
        'database',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉None值
a.datas = [x for x in a.datas if x is not None]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='安全教育平台_Web版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('安全教育平台_Web版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ spec文件已创建")

def build_with_spec():
    """使用spec文件编译"""
    print("🔨 使用spec文件编译...")
    
    cmd = [
        'pyinstaller',
        '--clean',
        '安全教育平台_Web版.spec'
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 编译成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 编译失败: {e}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("📦 创建便携版包...")
    
    package_dir = Path('安全教育平台_Web版_便携版')
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 复制可执行文件
    exe_file = Path('dist/安全教育平台_Web版.exe')
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir)
        print(f"✅ 已复制: {exe_file.name}")
    else:
        print("❌ 找不到可执行文件")
        return False
    
    # 复制模板文件
    if os.path.exists('templates'):
        shutil.copytree('templates', package_dir / 'templates')
        print("✅ 已复制: templates")
    
    # 复制数据库文件
    if os.path.exists('data.db'):
        shutil.copy2('data.db', package_dir)
        print("✅ 已复制: data.db")
    
    return package_dir

def create_final_launchers(package_dir):
    """创建最终的启动脚本"""
    print("🚀 创建最终启动脚本...")
    
    # 主启动脚本
    main_launcher = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo ========================================
echo.

echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo.

cd /d "%~dp0"
start "安全教育平台Web版" "%~dp0安全教育平台_Web版.exe"

echo ✅ 程序已启动
echo 📱 请在浏览器中访问: http://localhost:5000
echo.
pause
'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='utf-8') as f:
        f.write(main_launcher)
    
    # 静默启动脚本
    silent_launcher = '''@echo off
cd /d "%~dp0"
start "" "%~dp0安全教育平台_Web版.exe"
'''
    
    with open(package_dir / '静默启动.bat', 'w', encoding='utf-8') as f:
        f.write(silent_launcher)
    
    # 调试启动脚本
    debug_launcher = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版 - 调试模式

echo.
echo ========================================
echo   安全教育平台Web版 - 调试模式
echo ========================================
echo.

cd /d "%~dp0"
"%~dp0安全教育平台_Web版.exe"

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
'''
    
    with open(package_dir / '调试启动.bat', 'w', encoding='utf-8') as f:
        f.write(debug_launcher)
    
    print("✅ 启动脚本已创建")

def create_readme(package_dir):
    """创建说明文档"""
    print("📖 创建说明文档...")
    
    readme_content = '''# 安全教育平台Web版 v2.0 - 便携版

## 🚀 快速开始

### 推荐方式
1. 双击 `启动程序.bat`
2. 等待程序启动
3. 在浏览器中访问 http://localhost:5000

### 其他方式
- `静默启动.bat` - 后台启动，不显示窗口
- `调试启动.bat` - 显示详细错误信息
- 直接双击 `安全教育平台_Web版.exe`

## 🌟 功能特点

- ✅ **Web可视化界面** - 实时显示学习进度
- ✅ **多角色支持** - 支持学生、教师、管理员模式
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **便携设计** - 无需安装，解压即用
- ✅ **错误处理** - 完善的错误处理和重试机制

## 🎯 使用步骤

1. **启动程序** - 双击任意启动脚本
2. **打开界面** - 在浏览器中访问 http://localhost:5000
3. **填写信息** - 输入用户名、密码，选择角色
4. **开始学习** - 点击"开始任务"按钮
5. **查看进度** - 实时查看学习进度和日志

## 🎭 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 尝试使用 `调试启动.bat` 查看错误信息
   - 检查是否有杀毒软件拦截
   - 确保Windows系统版本兼容

2. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查防火墙设置
   - 确认端口5000未被占用

3. **登录失败**
   - 检查账号密码是否正确
   - 确认网络连接正常
   - 检查是否频繁登录被限制

## 📋 系统要求

- **操作系统**: Windows 10/11
- **内存**: 至少 2GB RAM
- **网络**: 稳定的互联网连接
- **浏览器**: Chrome、Firefox、Edge等现代浏览器

## 🔒 安全说明

- 本程序已进行代码编译处理
- 不会收集或泄露用户个人信息
- 仅用于自动化学习，请合理使用

---

**版本**: v2.0 便携版  
**更新时间**: 2024年  
**兼容系统**: Windows 10/11
'''
    
    with open(package_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 说明文档已创建")

def create_zip_package(package_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    # 删除旧的压缩包
    if os.path.exists(zip_name):
        os.remove(zip_name)
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 压缩包已创建: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🎯 安全教育平台Web版最终打包工具")
    print("=" * 50)
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建spec文件
    create_spec_file()
    
    # 使用spec文件编译
    if not build_with_spec():
        return False
    
    # 创建便携版包
    package_dir = create_portable_package()
    if not package_dir:
        return False
    
    # 创建启动脚本
    create_final_launchers(package_dir)
    
    # 创建说明文档
    create_readme(package_dir)
    
    # 创建ZIP压缩包
    zip_file = create_zip_package(package_dir)
    
    print("\n🎉 最终打包完成！")
    print(f"📁 便携版目录: {package_dir}")
    print(f"📦 压缩包: {zip_file}")
    print(f"📏 压缩包大小: {os.path.getsize(zip_file) / 1024 / 1024:.1f} MB")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件到任意目录")
    print("2. 双击运行 '启动程序.bat'")
    print("3. 在浏览器中使用Web界面")
    print("\n✨ 特点:")
    print("- 📦 真正的便携版，包含所有依赖")
    print("- 🚀 多种启动方式可选")
    print("- 🔧 包含调试模式")
    print("- 📖 详细的使用说明")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ 打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        sys.exit(1)
