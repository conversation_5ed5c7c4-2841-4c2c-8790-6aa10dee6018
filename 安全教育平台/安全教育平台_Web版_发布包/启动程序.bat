@echo off
chcp 65001 >nul
title 安全教育平台Web版

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo ========================================
echo.

echo 🔍 检查程序文件...
if not exist "%~dp0安全教育平台_Web版.exe" (
    echo ❌ 错误：找不到程序文件
    echo 📁 请确保此脚本与程序文件在同一目录下
    pause
    exit /b 1
)
echo ✅ 程序文件检查通过

echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.

echo 🚀 正在启动程序...
cd /d "%~dp0"
start "安全教育平台Web版" "%~dp0安全教育平台_Web版.exe"

echo.
echo ⏳ 等待程序启动...
timeout /t 5 /nobreak >nul

echo ✅ 程序已启动
echo 📱 请在浏览器中访问: http://localhost:5000
echo.
echo 💡 使用提示：
echo    • 如果浏览器没有自动打开，请手动访问上述地址
echo    • 程序窗口会显示详细的运行日志
echo    • 如需停止程序，请关闭程序窗口
echo.
pause
