(['E:\\code\\orders\\安全教育平台\\start_web.py'],
 ['E:\\code\\orders'],
 [],
 [('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('app.py', 'E:\\code\\orders\\安全教育平台\\app.py', 'DATA'),
  ('database.py', 'E:\\code\\orders\\安全教育平台\\database.py', 'DATA'),
  ('index.py', 'E:\\code\\orders\\安全教育平台\\index.py', 'DATA'),
  ('templates\\index_new.html',
   'E:\\code\\orders\\安全教育平台\\templates\\index_new.html',
   'DATA')],
 '3.12.8 (tags/v3.12.8:2dc476b, Dec  3 2024, 19:30:04) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('start_web', 'E:\\code\\orders\\安全教育平台\\start_web.py', 'PYSOURCE')],
 [('zipfile',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('struct', 'C:\\Program Files\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python312\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python312\\Lib\\shlex.py', 'PYMODULE')],
 [('python312.dll', 'C:\\Program Files\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('app.py', 'E:\\code\\orders\\安全教育平台\\app.py', 'DATA'),
  ('database.py', 'E:\\code\\orders\\安全教育平台\\database.py', 'DATA'),
  ('index.py', 'E:\\code\\orders\\安全教育平台\\index.py', 'DATA'),
  ('templates\\index_new.html',
   'E:\\code\\orders\\安全教育平台\\templates\\index_new.html',
   'DATA'),
  ('base_library.zip',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_修复版\\base_library.zip',
   'DATA')],
 [('ntpath', 'C:\\Program Files\\Python312\\Lib\\ntpath.py', 'PYMODULE'),
  ('io', 'C:\\Program Files\\Python312\\Lib\\io.py', 'PYMODULE'),
  ('heapq', 'C:\\Program Files\\Python312\\Lib\\heapq.py', 'PYMODULE'),
  ('reprlib', 'C:\\Program Files\\Python312\\Lib\\reprlib.py', 'PYMODULE'),
  ('genericpath',
   'C:\\Program Files\\Python312\\Lib\\genericpath.py',
   'PYMODULE'),
  ('copyreg', 'C:\\Program Files\\Python312\\Lib\\copyreg.py', 'PYMODULE'),
  ('traceback', 'C:\\Program Files\\Python312\\Lib\\traceback.py', 'PYMODULE'),
  ('functools', 'C:\\Program Files\\Python312\\Lib\\functools.py', 'PYMODULE'),
  ('abc', 'C:\\Program Files\\Python312\\Lib\\abc.py', 'PYMODULE'),
  ('stat', 'C:\\Program Files\\Python312\\Lib\\stat.py', 'PYMODULE'),
  ('weakref', 'C:\\Program Files\\Python312\\Lib\\weakref.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Program Files\\Python312\\Lib\\sre_parse.py', 'PYMODULE'),
  ('operator', 'C:\\Program Files\\Python312\\Lib\\operator.py', 'PYMODULE'),
  ('_collections_abc',
   'C:\\Program Files\\Python312\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('warnings', 'C:\\Program Files\\Python312\\Lib\\warnings.py', 'PYMODULE'),
  ('types', 'C:\\Program Files\\Python312\\Lib\\types.py', 'PYMODULE'),
  ('enum', 'C:\\Program Files\\Python312\\Lib\\enum.py', 'PYMODULE'),
  ('linecache', 'C:\\Program Files\\Python312\\Lib\\linecache.py', 'PYMODULE'),
  ('re._parser',
   'C:\\Program Files\\Python312\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Program Files\\Python312\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Program Files\\Python312\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Program Files\\Python312\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re', 'C:\\Program Files\\Python312\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('_weakrefset',
   'C:\\Program Files\\Python312\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Program Files\\Python312\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Program Files\\Python312\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('posixpath', 'C:\\Program Files\\Python312\\Lib\\posixpath.py', 'PYMODULE'),
  ('locale', 'C:\\Program Files\\Python312\\Lib\\locale.py', 'PYMODULE'),
  ('sre_constants',
   'C:\\Program Files\\Python312\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('keyword', 'C:\\Program Files\\Python312\\Lib\\keyword.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Program Files\\Python312\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Program Files\\Python312\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Program Files\\Python312\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Program Files\\Python312\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Program Files\\Python312\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Program Files\\Python312\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Program Files\\Python312\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Program Files\\Python312\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Program Files\\Python312\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Program Files\\Python312\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Program Files\\Python312\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Program Files\\Python312\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Program Files\\Python312\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Program Files\\Python312\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Program Files\\Python312\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Program Files\\Python312\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Program Files\\Python312\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Program Files\\Python312\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Program Files\\Python312\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Program Files\\Python312\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Program Files\\Python312\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Program Files\\Python312\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Program Files\\Python312\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Program Files\\Python312\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Program Files\\Python312\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Program Files\\Python312\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Program Files\\Python312\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Program Files\\Python312\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Program Files\\Python312\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Program Files\\Python312\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Program Files\\Python312\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Program Files\\Python312\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Program Files\\Python312\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Program Files\\Python312\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Program Files\\Python312\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Program Files\\Python312\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Program Files\\Python312\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Program Files\\Python312\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Program Files\\Python312\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Program Files\\Python312\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Program Files\\Python312\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Program Files\\Python312\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Program Files\\Python312\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Program Files\\Python312\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Program Files\\Python312\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('codecs', 'C:\\Program Files\\Python312\\Lib\\codecs.py', 'PYMODULE'),
  ('os', 'C:\\Program Files\\Python312\\Lib\\os.py', 'PYMODULE')])
