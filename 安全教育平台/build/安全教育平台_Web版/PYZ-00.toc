('E:\\code\\orders\\安全教育平台\\build\\安全教育平台_Web版\\PYZ-00.pyz',
 [('__future__',
   'C:\\Program Files\\Python312\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python312\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Program Files\\Python312\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files\\Python312\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Program Files\\Python312\\Lib\\bdb.py', 'PYMODULE'),
  ('bidict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\__init__.py',
   'PYMODULE'),
  ('bidict._abc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_abc.py',
   'PYMODULE'),
  ('bidict._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_base.py',
   'PYMODULE'),
  ('bidict._bidict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_bidict.py',
   'PYMODULE'),
  ('bidict._dup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_dup.py',
   'PYMODULE'),
  ('bidict._exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_exc.py',
   'PYMODULE'),
  ('bidict._frozen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_frozen.py',
   'PYMODULE'),
  ('bidict._iter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_iter.py',
   'PYMODULE'),
  ('bidict._orderedbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_orderedbase.py',
   'PYMODULE'),
  ('bidict._orderedbidict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_orderedbidict.py',
   'PYMODULE'),
  ('bidict._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\_typing.py',
   'PYMODULE'),
  ('bidict.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bidict\\metadata.py',
   'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi', 'C:\\Program Files\\Python312\\Lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Program Files\\Python312\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Program Files\\Python312\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Program Files\\Python312\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python312\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python312\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Program Files\\Python312\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('engineio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\__init__.py',
   'PYMODULE'),
  ('engineio.async_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\async_client.py',
   'PYMODULE'),
  ('engineio.async_drivers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\async_drivers\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers.asgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\async_drivers\\asgi.py',
   'PYMODULE'),
  ('engineio.async_drivers.tornado',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\async_drivers\\tornado.py',
   'PYMODULE'),
  ('engineio.async_server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\async_server.py',
   'PYMODULE'),
  ('engineio.async_socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\async_socket.py',
   'PYMODULE'),
  ('engineio.base_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\base_client.py',
   'PYMODULE'),
  ('engineio.base_server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\base_server.py',
   'PYMODULE'),
  ('engineio.base_socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\base_socket.py',
   'PYMODULE'),
  ('engineio.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\client.py',
   'PYMODULE'),
  ('engineio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\exceptions.py',
   'PYMODULE'),
  ('engineio.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\json.py',
   'PYMODULE'),
  ('engineio.middleware',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\middleware.py',
   'PYMODULE'),
  ('engineio.packet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\packet.py',
   'PYMODULE'),
  ('engineio.payload',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\payload.py',
   'PYMODULE'),
  ('engineio.server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\server.py',
   'PYMODULE'),
  ('engineio.socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\socket.py',
   'PYMODULE'),
  ('engineio.static_files',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\engineio\\static_files.py',
   'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_socketio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask_socketio\\__init__.py',
   'PYMODULE'),
  ('flask_socketio.namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask_socketio\\namespace.py',
   'PYMODULE'),
  ('flask_socketio.test_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask_socketio\\test_client.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python312\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python312\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Program Files\\Python312\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python312\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python312\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python312\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python312\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Program Files\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('loguru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\__init__.py',
   'PYMODULE'),
  ('loguru._asyncio_loop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_asyncio_loop.py',
   'PYMODULE'),
  ('loguru._better_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_better_exceptions.py',
   'PYMODULE'),
  ('loguru._colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_colorama.py',
   'PYMODULE'),
  ('loguru._colorizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_colorizer.py',
   'PYMODULE'),
  ('loguru._contextvars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_contextvars.py',
   'PYMODULE'),
  ('loguru._ctime_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_ctime_functions.py',
   'PYMODULE'),
  ('loguru._datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_datetime.py',
   'PYMODULE'),
  ('loguru._defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_defaults.py',
   'PYMODULE'),
  ('loguru._error_interceptor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_error_interceptor.py',
   'PYMODULE'),
  ('loguru._file_sink',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_file_sink.py',
   'PYMODULE'),
  ('loguru._filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_filters.py',
   'PYMODULE'),
  ('loguru._get_frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_get_frame.py',
   'PYMODULE'),
  ('loguru._handler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_handler.py',
   'PYMODULE'),
  ('loguru._locks_machinery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_locks_machinery.py',
   'PYMODULE'),
  ('loguru._logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_logger.py',
   'PYMODULE'),
  ('loguru._recattrs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_recattrs.py',
   'PYMODULE'),
  ('loguru._simple_sinks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_simple_sinks.py',
   'PYMODULE'),
  ('loguru._string_parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\loguru\\_string_parsers.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('msgpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\msgpack\\__init__.py',
   'PYMODULE'),
  ('msgpack.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\msgpack\\exceptions.py',
   'PYMODULE'),
  ('msgpack.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\msgpack\\ext.py',
   'PYMODULE'),
  ('msgpack.fallback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\msgpack\\fallback.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python312\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python312\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Program Files\\Python312\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python312\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('redis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\__init__.py',
   'PYMODULE'),
  ('redis._parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\__init__.py',
   'PYMODULE'),
  ('redis._parsers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\base.py',
   'PYMODULE'),
  ('redis._parsers.commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\commands.py',
   'PYMODULE'),
  ('redis._parsers.encoders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\encoders.py',
   'PYMODULE'),
  ('redis._parsers.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\helpers.py',
   'PYMODULE'),
  ('redis._parsers.hiredis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\hiredis.py',
   'PYMODULE'),
  ('redis._parsers.resp2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\resp2.py',
   'PYMODULE'),
  ('redis._parsers.resp3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\resp3.py',
   'PYMODULE'),
  ('redis._parsers.socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\_parsers\\socket.py',
   'PYMODULE'),
  ('redis.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\__init__.py',
   'PYMODULE'),
  ('redis.asyncio.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\client.py',
   'PYMODULE'),
  ('redis.asyncio.cluster',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\cluster.py',
   'PYMODULE'),
  ('redis.asyncio.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\connection.py',
   'PYMODULE'),
  ('redis.asyncio.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\lock.py',
   'PYMODULE'),
  ('redis.asyncio.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\retry.py',
   'PYMODULE'),
  ('redis.asyncio.sentinel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\sentinel.py',
   'PYMODULE'),
  ('redis.asyncio.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\asyncio\\utils.py',
   'PYMODULE'),
  ('redis.backoff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\backoff.py',
   'PYMODULE'),
  ('redis.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\cache.py',
   'PYMODULE'),
  ('redis.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\client.py',
   'PYMODULE'),
  ('redis.cluster',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\cluster.py',
   'PYMODULE'),
  ('redis.commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\__init__.py',
   'PYMODULE'),
  ('redis.commands.bf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\bf\\__init__.py',
   'PYMODULE'),
  ('redis.commands.bf.commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\bf\\commands.py',
   'PYMODULE'),
  ('redis.commands.bf.info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\bf\\info.py',
   'PYMODULE'),
  ('redis.commands.cluster',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\cluster.py',
   'PYMODULE'),
  ('redis.commands.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\core.py',
   'PYMODULE'),
  ('redis.commands.graph',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\__init__.py',
   'PYMODULE'),
  ('redis.commands.graph.commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\commands.py',
   'PYMODULE'),
  ('redis.commands.graph.edge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\edge.py',
   'PYMODULE'),
  ('redis.commands.graph.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\exceptions.py',
   'PYMODULE'),
  ('redis.commands.graph.execution_plan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\execution_plan.py',
   'PYMODULE'),
  ('redis.commands.graph.node',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\node.py',
   'PYMODULE'),
  ('redis.commands.graph.path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\path.py',
   'PYMODULE'),
  ('redis.commands.graph.query_result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\graph\\query_result.py',
   'PYMODULE'),
  ('redis.commands.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\helpers.py',
   'PYMODULE'),
  ('redis.commands.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\json\\__init__.py',
   'PYMODULE'),
  ('redis.commands.json._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\json\\_util.py',
   'PYMODULE'),
  ('redis.commands.json.commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\json\\commands.py',
   'PYMODULE'),
  ('redis.commands.json.decoders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\json\\decoders.py',
   'PYMODULE'),
  ('redis.commands.json.path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\json\\path.py',
   'PYMODULE'),
  ('redis.commands.redismodules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\redismodules.py',
   'PYMODULE'),
  ('redis.commands.search',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\__init__.py',
   'PYMODULE'),
  ('redis.commands.search._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\_util.py',
   'PYMODULE'),
  ('redis.commands.search.aggregation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\aggregation.py',
   'PYMODULE'),
  ('redis.commands.search.commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\commands.py',
   'PYMODULE'),
  ('redis.commands.search.document',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\document.py',
   'PYMODULE'),
  ('redis.commands.search.field',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\field.py',
   'PYMODULE'),
  ('redis.commands.search.indexDefinition',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\indexDefinition.py',
   'PYMODULE'),
  ('redis.commands.search.query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\query.py',
   'PYMODULE'),
  ('redis.commands.search.result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\result.py',
   'PYMODULE'),
  ('redis.commands.search.suggestion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\search\\suggestion.py',
   'PYMODULE'),
  ('redis.commands.sentinel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\sentinel.py',
   'PYMODULE'),
  ('redis.commands.timeseries',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\timeseries\\__init__.py',
   'PYMODULE'),
  ('redis.commands.timeseries.commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\timeseries\\commands.py',
   'PYMODULE'),
  ('redis.commands.timeseries.info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\timeseries\\info.py',
   'PYMODULE'),
  ('redis.commands.timeseries.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\commands\\timeseries\\utils.py',
   'PYMODULE'),
  ('redis.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\connection.py',
   'PYMODULE'),
  ('redis.crc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\crc.py',
   'PYMODULE'),
  ('redis.credentials',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\credentials.py',
   'PYMODULE'),
  ('redis.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\exceptions.py',
   'PYMODULE'),
  ('redis.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\lock.py',
   'PYMODULE'),
  ('redis.ocsp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\ocsp.py',
   'PYMODULE'),
  ('redis.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\retry.py',
   'PYMODULE'),
  ('redis.sentinel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\sentinel.py',
   'PYMODULE'),
  ('redis.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\typing.py',
   'PYMODULE'),
  ('redis.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis\\utils.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python312\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('simple_websocket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\simple_websocket\\__init__.py',
   'PYMODULE'),
  ('simple_websocket.aiows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\simple_websocket\\aiows.py',
   'PYMODULE'),
  ('simple_websocket.asgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\simple_websocket\\asgi.py',
   'PYMODULE'),
  ('simple_websocket.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\simple_websocket\\errors.py',
   'PYMODULE'),
  ('simple_websocket.ws',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\simple_websocket\\ws.py',
   'PYMODULE'),
  ('site', 'C:\\Program Files\\Python312\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('socketio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\__init__.py',
   'PYMODULE'),
  ('socketio.admin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\admin.py',
   'PYMODULE'),
  ('socketio.asgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\asgi.py',
   'PYMODULE'),
  ('socketio.async_admin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_admin.py',
   'PYMODULE'),
  ('socketio.async_aiopika_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_aiopika_manager.py',
   'PYMODULE'),
  ('socketio.async_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_client.py',
   'PYMODULE'),
  ('socketio.async_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_manager.py',
   'PYMODULE'),
  ('socketio.async_namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_namespace.py',
   'PYMODULE'),
  ('socketio.async_pubsub_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_pubsub_manager.py',
   'PYMODULE'),
  ('socketio.async_redis_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_redis_manager.py',
   'PYMODULE'),
  ('socketio.async_server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_server.py',
   'PYMODULE'),
  ('socketio.async_simple_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\async_simple_client.py',
   'PYMODULE'),
  ('socketio.base_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\base_client.py',
   'PYMODULE'),
  ('socketio.base_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\base_manager.py',
   'PYMODULE'),
  ('socketio.base_namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\base_namespace.py',
   'PYMODULE'),
  ('socketio.base_server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\base_server.py',
   'PYMODULE'),
  ('socketio.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\client.py',
   'PYMODULE'),
  ('socketio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\exceptions.py',
   'PYMODULE'),
  ('socketio.kafka_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\kafka_manager.py',
   'PYMODULE'),
  ('socketio.kombu_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\kombu_manager.py',
   'PYMODULE'),
  ('socketio.manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\manager.py',
   'PYMODULE'),
  ('socketio.middleware',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\middleware.py',
   'PYMODULE'),
  ('socketio.msgpack_packet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\msgpack_packet.py',
   'PYMODULE'),
  ('socketio.namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\namespace.py',
   'PYMODULE'),
  ('socketio.packet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\packet.py',
   'PYMODULE'),
  ('socketio.pubsub_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\pubsub_manager.py',
   'PYMODULE'),
  ('socketio.redis_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\redis_manager.py',
   'PYMODULE'),
  ('socketio.server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\server.py',
   'PYMODULE'),
  ('socketio.simple_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\simple_client.py',
   'PYMODULE'),
  ('socketio.tornado',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\tornado.py',
   'PYMODULE'),
  ('socketio.zmq_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\socketio\\zmq_manager.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python312\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files\\Python312\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python312\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Program Files\\Python312\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python312\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python312\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program Files\\Python312\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python312\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python312\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python312\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files\\Python312\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python312\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python312\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python312\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python312\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python312\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python312\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python312\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python312\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('win32_setctime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32_setctime\\__init__.py',
   'PYMODULE'),
  ('win32_setctime._setctime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\win32_setctime\\_setctime.py',
   'PYMODULE'),
  ('wsproto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('zstandard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
