('E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\安全教育平台_完整版.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('start_web', 'E:\\code\\orders\\安全教育平台\\start_web.py', 'PYSOURCE'),
  ('python312.dll', 'C:\\Program Files\\Python312\\python312.dll', 'BINARY'),
  ('_lzma.pyd', 'C:\\Program Files\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('lxml\\sax.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\sax.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\objectify.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\diff.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\etree.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\builder.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\_elementpath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('zstandard\\_cffi.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\_cffi.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\zstandard\\backend_c.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('msgpack\\_cmsgpack.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\msgpack\\_cmsgpack.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python312\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python312\\python3.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python312\\DLLs\\sqlite3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('Flask_SocketIO-5.5.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\Flask_SocketIO-5.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('Flask_SocketIO-5.5.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\Flask_SocketIO-5.5.1.dist-info\\LICENSE',
   'DATA'),
  ('Flask_SocketIO-5.5.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\Flask_SocketIO-5.5.1.dist-info\\METADATA',
   'DATA'),
  ('Flask_SocketIO-5.5.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\Flask_SocketIO-5.5.1.dist-info\\RECORD',
   'DATA'),
  ('Flask_SocketIO-5.5.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\Flask_SocketIO-5.5.1.dist-info\\REQUESTED',
   'DATA'),
  ('Flask_SocketIO-5.5.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\Flask_SocketIO-5.5.1.dist-info\\WHEEL',
   'DATA'),
  ('Flask_SocketIO-5.5.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\Flask_SocketIO-5.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('app.py', 'E:\\code\\orders\\安全教育平台\\app.py', 'DATA'),
  ('beautifulsoup4-4.13.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\beautifulsoup4-4.13.4.dist-info\\INSTALLER',
   'DATA'),
  ('beautifulsoup4-4.13.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\beautifulsoup4-4.13.4.dist-info\\METADATA',
   'DATA'),
  ('beautifulsoup4-4.13.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\beautifulsoup4-4.13.4.dist-info\\RECORD',
   'DATA'),
  ('beautifulsoup4-4.13.4.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\beautifulsoup4-4.13.4.dist-info\\REQUESTED',
   'DATA'),
  ('beautifulsoup4-4.13.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\beautifulsoup4-4.13.4.dist-info\\WHEEL',
   'DATA'),
  ('beautifulsoup4-4.13.4.dist-info\\licenses\\AUTHORS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\beautifulsoup4-4.13.4.dist-info\\licenses\\AUTHORS',
   'DATA'),
  ('beautifulsoup4-4.13.4.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\beautifulsoup4-4.13.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('bs4\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\__init__.py',
   'DATA'),
  ('bs4\\_deprecation.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_deprecation.py',
   'DATA'),
  ('bs4\\_typing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_typing.py',
   'DATA'),
  ('bs4\\_warnings.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\_warnings.py',
   'DATA'),
  ('bs4\\builder\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\__init__.py',
   'DATA'),
  ('bs4\\builder\\_html5lib.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_html5lib.py',
   'DATA'),
  ('bs4\\builder\\_htmlparser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_htmlparser.py',
   'DATA'),
  ('bs4\\builder\\_lxml.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\builder\\_lxml.py',
   'DATA'),
  ('bs4\\css.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\css.py',
   'DATA'),
  ('bs4\\dammit.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\dammit.py',
   'DATA'),
  ('bs4\\diagnose.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\diagnose.py',
   'DATA'),
  ('bs4\\element.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\element.py',
   'DATA'),
  ('bs4\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\exceptions.py',
   'DATA'),
  ('bs4\\filter.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\filter.py',
   'DATA'),
  ('bs4\\formatter.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\formatter.py',
   'DATA'),
  ('bs4\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\py.typed',
   'DATA'),
  ('bs4\\tests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\__init__.py',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-4670634698080256.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-4670634698080256.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-4818336571064320.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-4818336571064320.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-4999465949331456.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-4999465949331456.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5000587759190016.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5000587759190016.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5167584867909632.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5167584867909632.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5270998950477824.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5270998950477824.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5375146639360000.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5375146639360000.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5492400320282624.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5492400320282624.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5703933063462912.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5703933063462912.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5843991618256896.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5843991618256896.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5984173902397440.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-5984173902397440.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6124268085182464.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6124268085182464.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6241471367348224.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6241471367348224.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6306874195312640.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6306874195312640.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6450958476902400.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6450958476902400.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6600557255327744.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\clusterfuzz-testcase-minimized-bs4_fuzzer-6600557255327744.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\crash-0d306a50c8ed8bcd0785b67000fcd5dea1d33f08.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\crash-0d306a50c8ed8bcd0785b67000fcd5dea1d33f08.testcase',
   'DATA'),
  ('bs4\\tests\\fuzz\\crash-ffbdfa8a2b26f13537b68d3794b0478a4090ee4a.testcase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\fuzz\\crash-ffbdfa8a2b26f13537b68d3794b0478a4090ee4a.testcase',
   'DATA'),
  ('bs4\\tests\\test_builder.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_builder.py',
   'DATA'),
  ('bs4\\tests\\test_builder_registry.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_builder_registry.py',
   'DATA'),
  ('bs4\\tests\\test_css.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_css.py',
   'DATA'),
  ('bs4\\tests\\test_dammit.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_dammit.py',
   'DATA'),
  ('bs4\\tests\\test_element.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_element.py',
   'DATA'),
  ('bs4\\tests\\test_filter.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_filter.py',
   'DATA'),
  ('bs4\\tests\\test_formatter.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_formatter.py',
   'DATA'),
  ('bs4\\tests\\test_fuzz.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_fuzz.py',
   'DATA'),
  ('bs4\\tests\\test_html5lib.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_html5lib.py',
   'DATA'),
  ('bs4\\tests\\test_htmlparser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_htmlparser.py',
   'DATA'),
  ('bs4\\tests\\test_lxml.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_lxml.py',
   'DATA'),
  ('bs4\\tests\\test_navigablestring.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_navigablestring.py',
   'DATA'),
  ('bs4\\tests\\test_pageelement.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_pageelement.py',
   'DATA'),
  ('bs4\\tests\\test_soup.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_soup.py',
   'DATA'),
  ('bs4\\tests\\test_tag.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_tag.py',
   'DATA'),
  ('bs4\\tests\\test_tree.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\bs4\\tests\\test_tree.py',
   'DATA'),
  ('data.db', 'E:\\code\\orders\\安全教育平台\\data.db', 'DATA'),
  ('database.py', 'E:\\code\\orders\\安全教育平台\\database.py', 'DATA'),
  ('flask-3.1.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('flask-3.1.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-3.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('flask\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\__init__.py',
   'DATA'),
  ('flask\\__main__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\__main__.py',
   'DATA'),
  ('flask\\app.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\app.py',
   'DATA'),
  ('flask\\blueprints.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\blueprints.py',
   'DATA'),
  ('flask\\cli.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\cli.py',
   'DATA'),
  ('flask\\config.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\config.py',
   'DATA'),
  ('flask\\ctx.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\ctx.py',
   'DATA'),
  ('flask\\debughelpers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\debughelpers.py',
   'DATA'),
  ('flask\\globals.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\globals.py',
   'DATA'),
  ('flask\\helpers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\helpers.py',
   'DATA'),
  ('flask\\json\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\json\\__init__.py',
   'DATA'),
  ('flask\\json\\provider.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\json\\provider.py',
   'DATA'),
  ('flask\\json\\tag.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\json\\tag.py',
   'DATA'),
  ('flask\\logging.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\logging.py',
   'DATA'),
  ('flask\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\py.typed',
   'DATA'),
  ('flask\\sansio\\README.md',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sansio\\README.md',
   'DATA'),
  ('flask\\sansio\\app.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sansio\\app.py',
   'DATA'),
  ('flask\\sansio\\blueprints.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sansio\\blueprints.py',
   'DATA'),
  ('flask\\sansio\\scaffold.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sansio\\scaffold.py',
   'DATA'),
  ('flask\\sessions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\sessions.py',
   'DATA'),
  ('flask\\signals.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\signals.py',
   'DATA'),
  ('flask\\templating.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\templating.py',
   'DATA'),
  ('flask\\testing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\testing.py',
   'DATA'),
  ('flask\\typing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\typing.py',
   'DATA'),
  ('flask\\views.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\views.py',
   'DATA'),
  ('flask\\wrappers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask\\wrappers.py',
   'DATA'),
  ('flask_socketio\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask_socketio\\__init__.py',
   'DATA'),
  ('flask_socketio\\namespace.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask_socketio\\namespace.py',
   'DATA'),
  ('flask_socketio\\test_client.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\flask_socketio\\test_client.py',
   'DATA'),
  ('index.py', 'E:\\code\\orders\\安全教育平台\\index.py', 'DATA'),
  ('lxml-5.4.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\INSTALLER',
   'DATA'),
  ('lxml-5.4.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\METADATA',
   'DATA'),
  ('lxml-5.4.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\RECORD',
   'DATA'),
  ('lxml-5.4.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\REQUESTED',
   'DATA'),
  ('lxml-5.4.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\WHEEL',
   'DATA'),
  ('lxml-5.4.0.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('lxml-5.4.0.dist-info\\licenses\\LICENSES.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\licenses\\LICENSES.txt',
   'DATA'),
  ('lxml-5.4.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml-5.4.0.dist-info\\top_level.txt',
   'DATA'),
  ('lxml\\ElementInclude.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\ElementInclude.py',
   'DATA'),
  ('lxml\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\__init__.py',
   'DATA'),
  ('lxml\\_elementpath.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\_elementpath.py',
   'DATA'),
  ('lxml\\apihelpers.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\apihelpers.pxi',
   'DATA'),
  ('lxml\\builder.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\builder.py',
   'DATA'),
  ('lxml\\classlookup.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\classlookup.pxi',
   'DATA'),
  ('lxml\\cleanup.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\cleanup.pxi',
   'DATA'),
  ('lxml\\cssselect.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\cssselect.py',
   'DATA'),
  ('lxml\\debug.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\debug.pxi',
   'DATA'),
  ('lxml\\docloader.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\docloader.pxi',
   'DATA'),
  ('lxml\\doctestcompare.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\doctestcompare.py',
   'DATA'),
  ('lxml\\dtd.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\dtd.pxi',
   'DATA'),
  ('lxml\\etree.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\etree.h',
   'DATA'),
  ('lxml\\etree.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\etree.pyx',
   'DATA'),
  ('lxml\\etree_api.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\etree_api.h',
   'DATA'),
  ('lxml\\extensions.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\extensions.pxi',
   'DATA'),
  ('lxml\\html\\ElementSoup.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\ElementSoup.py',
   'DATA'),
  ('lxml\\html\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\__init__.py',
   'DATA'),
  ('lxml\\html\\_diffcommand.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_diffcommand.py',
   'DATA'),
  ('lxml\\html\\_html5builder.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_html5builder.py',
   'DATA'),
  ('lxml\\html\\_setmixin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\_setmixin.py',
   'DATA'),
  ('lxml\\html\\builder.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\builder.py',
   'DATA'),
  ('lxml\\html\\clean.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\clean.py',
   'DATA'),
  ('lxml\\html\\defs.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\defs.py',
   'DATA'),
  ('lxml\\html\\diff.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\diff.py',
   'DATA'),
  ('lxml\\html\\formfill.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\formfill.py',
   'DATA'),
  ('lxml\\html\\html5parser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\html5parser.py',
   'DATA'),
  ('lxml\\html\\soupparser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\soupparser.py',
   'DATA'),
  ('lxml\\html\\usedoctest.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\html\\usedoctest.py',
   'DATA'),
  ('lxml\\includes\\__init__.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\__init__.pxd',
   'DATA'),
  ('lxml\\includes\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\__init__.py',
   'DATA'),
  ('lxml\\includes\\c14n.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\c14n.pxd',
   'DATA'),
  ('lxml\\includes\\config.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\config.pxd',
   'DATA'),
  ('lxml\\includes\\dtdvalid.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\dtdvalid.pxd',
   'DATA'),
  ('lxml\\includes\\etree_defs.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\etree_defs.h',
   'DATA'),
  ('lxml\\includes\\etreepublic.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\etreepublic.pxd',
   'DATA'),
  ('lxml\\includes\\extlibs\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'DATA'),
  ('lxml\\includes\\extlibs\\zconf.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\extlibs\\zconf.h',
   'DATA'),
  ('lxml\\includes\\extlibs\\zlib.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\extlibs\\zlib.h',
   'DATA'),
  ('lxml\\includes\\htmlparser.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\htmlparser.pxd',
   'DATA'),
  ('lxml\\includes\\libexslt\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'DATA'),
  ('lxml\\includes\\libexslt\\exslt.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libexslt\\exslt.h',
   'DATA'),
  ('lxml\\includes\\libexslt\\exsltconfig.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libexslt\\exsltconfig.h',
   'DATA'),
  ('lxml\\includes\\libexslt\\exsltexports.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libexslt\\exsltexports.h',
   'DATA'),
  ('lxml\\includes\\libexslt\\libexslt.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libexslt\\libexslt.h',
   'DATA'),
  ('lxml\\includes\\libxml\\HTMLparser.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\HTMLparser.h',
   'DATA'),
  ('lxml\\includes\\libxml\\HTMLtree.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\HTMLtree.h',
   'DATA'),
  ('lxml\\includes\\libxml\\SAX.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\SAX.h',
   'DATA'),
  ('lxml\\includes\\libxml\\SAX2.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\SAX2.h',
   'DATA'),
  ('lxml\\includes\\libxml\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'DATA'),
  ('lxml\\includes\\libxml\\c14n.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\c14n.h',
   'DATA'),
  ('lxml\\includes\\libxml\\catalog.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\catalog.h',
   'DATA'),
  ('lxml\\includes\\libxml\\chvalid.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\chvalid.h',
   'DATA'),
  ('lxml\\includes\\libxml\\debugXML.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\debugXML.h',
   'DATA'),
  ('lxml\\includes\\libxml\\dict.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\dict.h',
   'DATA'),
  ('lxml\\includes\\libxml\\encoding.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\encoding.h',
   'DATA'),
  ('lxml\\includes\\libxml\\entities.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\entities.h',
   'DATA'),
  ('lxml\\includes\\libxml\\globals.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\globals.h',
   'DATA'),
  ('lxml\\includes\\libxml\\hash.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\hash.h',
   'DATA'),
  ('lxml\\includes\\libxml\\list.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\list.h',
   'DATA'),
  ('lxml\\includes\\libxml\\nanoftp.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\nanoftp.h',
   'DATA'),
  ('lxml\\includes\\libxml\\nanohttp.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\nanohttp.h',
   'DATA'),
  ('lxml\\includes\\libxml\\parser.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\parser.h',
   'DATA'),
  ('lxml\\includes\\libxml\\parserInternals.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\parserInternals.h',
   'DATA'),
  ('lxml\\includes\\libxml\\pattern.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\pattern.h',
   'DATA'),
  ('lxml\\includes\\libxml\\relaxng.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\relaxng.h',
   'DATA'),
  ('lxml\\includes\\libxml\\schemasInternals.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\schemasInternals.h',
   'DATA'),
  ('lxml\\includes\\libxml\\schematron.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\schematron.h',
   'DATA'),
  ('lxml\\includes\\libxml\\threads.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\threads.h',
   'DATA'),
  ('lxml\\includes\\libxml\\tree.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\tree.h',
   'DATA'),
  ('lxml\\includes\\libxml\\uri.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\uri.h',
   'DATA'),
  ('lxml\\includes\\libxml\\valid.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\valid.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xinclude.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xinclude.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xlink.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xlink.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlIO.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlIO.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlautomata.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlautomata.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlerror.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlerror.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlexports.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlexports.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlmemory.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlmemory.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlmodule.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlmodule.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlreader.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlreader.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlregexp.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlregexp.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlsave.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlsave.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlschemas.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlschemas.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlschemastypes.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlschemastypes.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlstring.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlstring.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlunicode.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlunicode.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlversion.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlversion.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xmlwriter.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xmlwriter.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xpath.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xpath.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xpathInternals.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xpathInternals.h',
   'DATA'),
  ('lxml\\includes\\libxml\\xpointer.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxml\\xpointer.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'DATA'),
  ('lxml\\includes\\libxslt\\attributes.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\attributes.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\documents.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\documents.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\extensions.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\extensions.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\extra.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\extra.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\functions.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\functions.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\imports.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\imports.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\keys.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\keys.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\libxslt.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\libxslt.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\namespaces.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\namespaces.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\numbersInternals.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\numbersInternals.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\preproc.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\preproc.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\security.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\security.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\templates.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\templates.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\transform.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\transform.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\trio.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\trio.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\triodef.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\triodef.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\variables.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\variables.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\win32config.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\win32config.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\xslt.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\xslt.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\xsltInternals.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\xsltInternals.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\xsltconfig.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\xsltconfig.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\xsltexports.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\xsltexports.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\xsltlocale.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\xsltlocale.h',
   'DATA'),
  ('lxml\\includes\\libxslt\\xsltutils.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\libxslt\\xsltutils.h',
   'DATA'),
  ('lxml\\includes\\lxml-version.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\lxml-version.h',
   'DATA'),
  ('lxml\\includes\\relaxng.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\relaxng.pxd',
   'DATA'),
  ('lxml\\includes\\schematron.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\schematron.pxd',
   'DATA'),
  ('lxml\\includes\\tree.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\tree.pxd',
   'DATA'),
  ('lxml\\includes\\uri.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\uri.pxd',
   'DATA'),
  ('lxml\\includes\\xinclude.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\xinclude.pxd',
   'DATA'),
  ('lxml\\includes\\xmlerror.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\xmlerror.pxd',
   'DATA'),
  ('lxml\\includes\\xmlparser.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\xmlparser.pxd',
   'DATA'),
  ('lxml\\includes\\xmlschema.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\xmlschema.pxd',
   'DATA'),
  ('lxml\\includes\\xpath.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\xpath.pxd',
   'DATA'),
  ('lxml\\includes\\xslt.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\includes\\xslt.pxd',
   'DATA'),
  ('lxml\\isoschematron\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\__init__.py',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\iterparse.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\iterparse.pxi',
   'DATA'),
  ('lxml\\lxml.etree.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\lxml.etree.h',
   'DATA'),
  ('lxml\\lxml.etree_api.h',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\lxml.etree_api.h',
   'DATA'),
  ('lxml\\nsclasses.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\nsclasses.pxi',
   'DATA'),
  ('lxml\\objectify.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\objectify.pyx',
   'DATA'),
  ('lxml\\objectpath.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\objectpath.pxi',
   'DATA'),
  ('lxml\\parser.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\parser.pxi',
   'DATA'),
  ('lxml\\parsertarget.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\parsertarget.pxi',
   'DATA'),
  ('lxml\\proxy.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\proxy.pxi',
   'DATA'),
  ('lxml\\public-api.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\public-api.pxi',
   'DATA'),
  ('lxml\\pyclasslookup.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\pyclasslookup.py',
   'DATA'),
  ('lxml\\readonlytree.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\readonlytree.pxi',
   'DATA'),
  ('lxml\\relaxng.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\relaxng.pxi',
   'DATA'),
  ('lxml\\sax.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\sax.py',
   'DATA'),
  ('lxml\\saxparser.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\saxparser.pxi',
   'DATA'),
  ('lxml\\schematron.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\schematron.pxi',
   'DATA'),
  ('lxml\\serializer.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\serializer.pxi',
   'DATA'),
  ('lxml\\usedoctest.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\usedoctest.py',
   'DATA'),
  ('lxml\\xinclude.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\xinclude.pxi',
   'DATA'),
  ('lxml\\xmlerror.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\xmlerror.pxi',
   'DATA'),
  ('lxml\\xmlid.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\xmlid.pxi',
   'DATA'),
  ('lxml\\xmlschema.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\xmlschema.pxi',
   'DATA'),
  ('lxml\\xpath.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\xpath.pxi',
   'DATA'),
  ('lxml\\xslt.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\xslt.pxi',
   'DATA'),
  ('lxml\\xsltext.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\lxml\\xsltext.pxi',
   'DATA'),
  ('templates\\index_new.html',
   'E:\\code\\orders\\安全教育平台\\templates\\index_new.html',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('redis-5.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis-5.2.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('redis-5.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis-5.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('redis-5.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis-5.2.1.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('redis-5.2.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis-5.2.1.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('redis-5.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis-5.2.1.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('redis-5.2.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis-5.2.1.dist-info\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('redis-5.2.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\redis-5.2.1.dist-info\\top_level.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'E:\\code\\orders\\安全教育平台\\build\\安全教育平台_完整版\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
