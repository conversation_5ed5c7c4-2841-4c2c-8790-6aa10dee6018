��       �key_slot�.��       �self�.��       �encoder�.��
       �encode�.��       �
slots_to_keys�.��
       �append�.��D       �@Split keys into a dictionary that maps a slot to a list of keys.�.��	       �items�.��       �slots_to_pairs�.��
       �extend�.��F       �BSplit pairs into a dictionary that maps a slot to a list of pairs.�.��       �read_from_replicas�.��       �
READ_COMMANDS�.��       �pipeline�.��       �pipe�.��       �execute_command�.��       �command�.��       �target_nodes�.��       �
nodes_manager�.��       �get_node_from_slot�.��       �execute�.��
       �values�.��       �list_or_args�.��       �_partition_keys_by_slot�.��       �_execute_pipeline_by_slot�.��       �MGET�.��       �_reorder_keys_by_command�.��H      XA  
        Splits the keys into different slots and then calls MGET
        for the keys of every slot. This operation will not be atomic
        if keys belong to more than one slot.

        Returns a list of values ordered identically to ``keys``

        For more information see https://redis.io/commands/mget
        �.��       �_partition_pairs_by_slot�.��       �MSET�.���      X�  
        Sets key/values based on a mapping. Mapping is a dictionary of
        key/value pairs. Both keys and values should be strings or types that
        can be cast to a string via str().

        Splits the keys into different slots and then calls MSET
        for the keys of every slot. This operation will not be atomic
        if keys belong to more than one slot.

        For more information see https://redis.io/commands/mset
        �.��z       �v
        Runs the given command once for the keys
        of each slot. Returns the sum of the return values.
        �.��       �_split_command_across_slots�.��       �EXISTS���.��      ��
        Returns the number of ``names`` that exist in the
        whole cluster. The keys are first split up into slots
        and then an EXISTS command is sent for every slot

        For more information see https://redis.io/commands/exists
        �.��	       �DEL���.��C      X<  
        Deletes the given keys in the cluster.
        The keys are first split up into slots
        and then an DEL command is sent for every slot

        Non-existent keys are ignored.
        Returns the number of keys that were deleted.

        For more information see https://redis.io/commands/del
        �.��       �TOUCH���.��h      Xa  
        Updates the last access time of given keys across the
        cluster.

        The keys are first split up into slots
        and then an TOUCH command is sent for every slot

        Non-existent keys are ignored.
        Returns the number of keys that were touched.

        For more information see https://redis.io/commands/touch
        �.��       �UNLINK���.��T      XM  
        Remove the specified keys in a different thread.

        The keys are first split up into slots
        and then an TOUCH command is sent for every slot

        Non-existent keys are ignored.
        Returns the number of keys that were unlinked.

        For more information see https://redis.io/commands/unlink
        �.��       �keys�.��       �args�.��       �mget_nonatomic�.��/       �+AsyncClusterMultiKeyCommands.mget_nonatomic�.��       �mapping�.��       �mset_nonatomic�.��/       �+AsyncClusterMultiKeyCommands.mset_nonatomic�.��<       �8AsyncClusterMultiKeyCommands._split_command_across_slots�.��       �_initialize�.��       �
initialize�.��       �
slots_to_args�.��:       �6AsyncClusterMultiKeyCommands._execute_pipeline_by_slot�.��       �RedisClusterException�.��.       �(SLAVEOF is not supported in cluster mode���.���       ��
        Make the server a replica of another instance, or promote it as master.

        For more information see https://redis.io/commands/slaveof
        �.��0       �*REPLICAOF is not supported in cluster mode���.���       ��
        Make the server a replica of another instance, or promote it as master.

        For more information see https://redis.io/commands/replicaof
        �.��-       �'SWAPDB is not supported in cluster mode���.��s       �o
        Swaps two Redis databases.

        For more information see https://redis.io/commands/swapdb
        �.��       �CLUSTER MYID���.��       h��.���       ��
        Returns the node's id.

        :target_node: 'ClusterNode'
            The node to execute the command on

        For more information check https://redis.io/commands/cluster-myid/
        �.��       �CLUSTER ADDSLOTS���.���       ��
        Assign new hash slots to receiving node. Sends to specified node.

        :target_node: 'ClusterNode'
            The node to execute the command on

        For more information see https://redis.io/commands/cluster-addslots
        �.��       �CLUSTER ADDSLOTSRANGE���.���      X�  
        Similar to the CLUSTER ADDSLOTS command.
        The difference between the two commands is that ADDSLOTS takes a list of slots
        to assign to the node, while ADDSLOTSRANGE takes a list of slot ranges
        (specified by start and end slots) to assign to the node.

        :target_node: 'ClusterNode'
            The node to execute the command on

        For more information see https://redis.io/commands/cluster-addslotsrange
        �.��       �CLUSTER COUNTKEYSINSLOT�.���       ��
        Return the number of local keys in the specified hash slot
        Send to node based on specified slot_id

        For more information see https://redis.io/commands/cluster-countkeysinslot
        �.��!       �CLUSTER COUNT-FAILURE-REPORTS�.���       ��
        Return the number of failure reports active for a given node
        Sends to a random node

        For more information see https://redis.io/commands/cluster-count-failure-reports
        �.��       �CLUSTER DELSLOTS�.��      X  
        Set hash slots as unbound in the cluster.
        It determines by it self what node the slot is in and sends it there

        Returns a list of the results for each processed slot.

        For more information see https://redis.io/commands/cluster-delslots
        �.��       �CLUSTER DELSLOTSRANGE���.��X      XQ  
        Similar to the CLUSTER DELSLOTS command.
        The difference is that CLUSTER DELSLOTS takes a list of hash slots to remove
        from the node, while CLUSTER DELSLOTSRANGE takes a list of slot ranges to remove
        from the node.

        For more information see https://redis.io/commands/cluster-delslotsrange
        �.��	       �upper�.��       �FORCE��TAKEOVER���.��       �
RedisError�.��1       �-Invalid option for CLUSTER FAILOVER command: �.��       � �.��       �CLUSTER FAILOVER�.��       �CLUSTER FAILOVER���.��      X  
        Forces a slave to perform a manual failover of its master
        Sends to specified node

        :target_node: 'ClusterNode'
            The node to execute the command on

        For more information see https://redis.io/commands/cluster-failover
        �.��       �CLUSTER INFO���.���       ��
        Provides info about Redis Cluster node state.
        The command will be sent to a random node in the cluster if no target
        node is specified.

        For more information see https://redis.io/commands/cluster-info
        �.��       �CLUSTER KEYSLOT�.���       ��
        Returns the hash slot of the specified key
        Sends to random node in the cluster

        For more information see https://redis.io/commands/cluster-keyslot
        �.��       �CLUSTER MEET�.���       ��
        Force a node cluster to handshake with another node.
        Sends to specified node.

        For more information see https://redis.io/commands/cluster-meet
        �.��       �
CLUSTER NODES���.���       ��
        Get Cluster config for the node.
        Sends to random node in the cluster

        For more information see https://redis.io/commands/cluster-nodes
        �.��       �CLUSTER REPLICATE�.���       ��
        Reconfigure a node as a slave of the specified master node

        For more information see https://redis.io/commands/cluster-replicate
        �.��       �
CLUSTER RESET�.��       CSOFT�.��       CHARD�.���       ��
        Reset a Redis Cluster node

        If 'soft' is True then it will send 'SOFT' argument
        If 'soft' is False then it will send 'HARD' argument

        For more information see https://redis.io/commands/cluster-reset
        �.��       �CLUSTER SAVECONFIG���.���       ��
        Forces the node to save cluster state on disk

        For more information see https://redis.io/commands/cluster-saveconfig
        �.��       �CLUSTER GETKEYSINSLOT�.���       ��
        Returns the number of keys in the specified cluster slot

        For more information see https://redis.io/commands/cluster-getkeysinslot
        �.��       �CLUSTER SET-CONFIG-EPOCH�.���       ��
        Set the configuration epoch in a new node

        For more information see https://redis.io/commands/cluster-set-config-epoch
        �.��"       �	IMPORTING��NODE��	MIGRATING���.��       �CLUSTER SETSLOT�.��
       �STABLE�.��:       �4For "stable" state please use cluster_setslot_stable���.��       �Invalid slot state: �.���       ��
        Bind an hash slot to a specific node

        :target_node: 'ClusterNode'
            The node to execute the command on

        For more information see https://redis.io/commands/cluster-setslot
        �.���       ��
        Clears migrating / importing state from the slot.
        It determines by it self what node the slot is in and sends it there.

        For more information see https://redis.io/commands/cluster-setslot
        �.��       �CLUSTER REPLICAS�.���       ��
        Provides a list of replica nodes replicating from the specified primary
        target node.

        For more information see https://redis.io/commands/cluster-replicas
        �.��       �
CLUSTER SLOTS���.���       ��
        Get array of Cluster slot to node mappings

        For more information see https://redis.io/commands/cluster-slots
        �.��       �CLUSTER SHARDS���.���       ��
        Returns details about the shards of the cluster.

        For more information see https://redis.io/commands/cluster-shards
        �.��       �CLUSTER MYSHARDID���.���       ��
        Returns the shard ID of the node.

        For more information see https://redis.io/commands/cluster-myshardid/
        �.��       �
CLUSTER LINKS���.���      X�  
        Each node in a Redis Cluster maintains a pair of long-lived TCP link with each
        peer in the cluster: One for sending outbound messages towards the peer and one
        for receiving inbound messages from the peer.

        This command outputs information of all such peer links as an array.

        For more information see https://redis.io/commands/cluster-links
        �.��F       �BCLUSTER FLUSHSLOTS is intentionally not implemented in the client.�.��E       �ACLUSTER BUMPEPOCH is intentionally not implemented in the client.�.��       �replicas�.��       �all�.��       �READONLY���.���       ��
        Enables read queries.
        The command will be sent to the default cluster node if target_nodes is
        not specified.

        For more information see https://redis.io/commands/readonly
        �.��       �	READWRITE���.���       ��
        Disables read queries.
        The command will be sent to the default cluster node if target_nodes is
        not specified.

        For more information see https://redis.io/commands/readwrite
        �.��!       �REDISGEARS_2.REFRESHCLUSTER���.��p       �l
        On an OSS cluster, before executing any gears function, you must call this command. # noqa
        �.��       �asyncio�.��
       �gather�.��	       �slots�.��       �cluster_delslots�.��3       �/AsyncClusterManagementCommands.cluster_delslots�.��       �create_task�.��
       �	<genexpr>�.��F       �BAsyncClusterManagementCommands.cluster_delslots.<locals>.<genexpr>�.��       �strings�.��       �default-node�.��
       �update�.��
       �	__class__�.��       �stralgo�.���      X�  
        Implements complex algorithms that operate on strings.
        Right now the only algorithm implemented is the LCS algorithm
        (longest common substring). However new algorithms could be
        implemented in the future.

        ``algo`` Right now must be LCS
        ``value1`` and ``value2`` Can be two strings or two keys
        ``specific_argument`` Specifying if the arguments to the algorithm
        will be keys or strings. strings is the default.
        ``len`` Returns just the len of the match.
        ``idx`` Returns the match positions in each string.
        ``minmatchlen`` Restrict the list of matches to the ones of a given
        minimal length. Can be provided only when ``idx`` set to True.
        ``withmatchlen`` Returns the matches with the len of the match.
        Can be provided only when ``idx`` set to True.

        For more information see https://redis.io/commands/stralgo
        �.��       �scan�.��	       �match�.��	       �count�.��	       �_type�.��
       �kwargs�.��       �get_node�.��       �	node_name���.��       �pop�.��       �target_nodes�N��.��       �cursors�.��
       �cursor�.��
       �	scan_iter�.��'       �#ClusterDataAccessCommands.scan_iter�.��,       �(AsyncClusterDataAccessCommands.scan_iter�.��       �__doc__�.��       �__file__�.��
       �origin�.��       �has_location�.��       �
__cached__�.��       �
TYPE_CHECKING�.��       �Any�.��       �
AsyncIterator�.��       �Dict�.��       �Iterable�.��       �Iterator�.��       �List�.��       �Literal�.��       �Mapping�.��       �NoReturn�.��       �Optional�.��	       �Union�.��
       �	redis.crc�.��       h ��.��       �redis.exceptions�.��       h9hZ��.��       �redis.typing�.��[       (�AnyKeyT��ClusterCommandsProtocol��
EncodableT��KeysT��KeyT��PatternT��	ResponseT�t�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.��       �core�.��2      (�ACLCommands��AsyncACLCommands��AsyncDataAccessCommands��AsyncFunctionCommands��AsyncGearsCommands��AsyncManagementCommands��AsyncModuleCommands��AsyncScriptCommands��DataAccessCommands��FunctionCommands��
GearsCommands��ManagementCommands��ModuleCommands��PubSubCommands��ScriptCommands�t�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.�h�.��       �helpers�.��       h��.��       �redismodules�.��4       �AsyncRedisModuleCommands��RedisModuleCommands���.�h�.�h�.���      (�GETBIT��BITPOS��GETRANGE��KEYS��HMGET��EVAL_RO��HGETALL��HLEN��SRANDMEMBER��LINDEX��TTL��GEOPOS��ZCARD��EXISTS��LLEN��SDIFF��HVALS��GET��HGET��HEXISTS��GEODIST��HKEYS��GEOHASH��SCARD��STRLEN��ZRANGE��
EVALSHA_RO��SINTER��	RANDOMKEY��SMEMBERS��SUNION��MGET��HSTRLEN��LRANGE��ZCOUNT��ZSCORE��BITCOUNT��	GEORADIUS��PTTL��	SISMEMBER��GEORADIUSBYMEMBER���.��       �__prepare__�.��       �ClusterMultiKeyCommands�.��       �__getitem__�.��2       �.%s.__prepare__() must return a mapping, not %s�.��       �__name__�.��       �<metaclass>�.��       �redis.commands.cluster�.��       �
__module__�.��G       �C
    A class containing commands that handle more than one key
    �.��       �__qualname__�.��
       �return�.��       �int�.��3       �/ClusterMultiKeyCommands._partition_keys_by_slot�.��4       �0ClusterMultiKeyCommands._partition_pairs_by_slot�.��       �str�.��5       �1ClusterMultiKeyCommands._execute_pipeline_by_slot�.��
       �	responses�.��4       �0ClusterMultiKeyCommands._reorder_keys_by_command�.��*       �&ClusterMultiKeyCommands.mget_nonatomic�.��       �bool�.��*       �&ClusterMultiKeyCommands.mset_nonatomic�.��7       �3ClusterMultiKeyCommands._split_command_across_slots�.��
       �exists�.��"       �ClusterMultiKeyCommands.exists�.��
       �delete�.��"       �ClusterMultiKeyCommands.delete�.��	       �touch�.��!       �ClusterMultiKeyCommands.touch�.��
       �unlink�.��"       �ClusterMultiKeyCommands.unlink�.��       �__orig_bases__�.��        �AsyncClusterMultiKeyCommands�.��       �ClusterManagementCommands�.���       ��
    A class for Redis Cluster management commands

    The class inherits from Redis's core ManagementCommands class and do the
    required adjustments to work with cluster mode
    �.��       �slaveof�.��%       �!ClusterManagementCommands.slaveof�.��
       �	replicaof�.��'       �#ClusterManagementCommands.replicaof�.��
       �swapdb�.��$       � ClusterManagementCommands.swapdb�.��       �target_node�.��       �TargetNodesT�.��       �cluster_myid�.��*       �&ClusterManagementCommands.cluster_myid�.��       �cluster_addslots�.��.       �*ClusterManagementCommands.cluster_addslots�.��       �cluster_addslotsrange�.��3       �/ClusterManagementCommands.cluster_addslotsrange�.��       �slot_id�.��       �cluster_countkeysinslot�.��5       �1ClusterManagementCommands.cluster_countkeysinslot�.��       �node_id�.��        �cluster_count_failure_report�.��:       �6ClusterManagementCommands.cluster_count_failure_report�.��.       �*ClusterManagementCommands.cluster_delslots�.��       �cluster_delslotsrange�.��3       �/ClusterManagementCommands.cluster_delslotsrange�.��       N��.��
       �option�.��       �cluster_failover�.��.       �*ClusterManagementCommands.cluster_failover�.��       �cluster_info�.��*       �&ClusterManagementCommands.cluster_info�.��       �key�.��       �cluster_keyslot�.��-       �)ClusterManagementCommands.cluster_keyslot�.��       �host�.��       �port�.��       �cluster_meet�.��*       �&ClusterManagementCommands.cluster_meet�.��       �
cluster_nodes�.��+       �'ClusterManagementCommands.cluster_nodes�.��       �cluster_replicate�.��/       �+ClusterManagementCommands.cluster_replicate�.��       �N��.��       �soft�.��       �
cluster_reset�.��+       �'ClusterManagementCommands.cluster_reset�.��       �cluster_save_config�.��1       �-ClusterManagementCommands.cluster_save_config�.��       �slot�.��       �num_keys�.��       �cluster_get_keys_in_slot�.��6       �2ClusterManagementCommands.cluster_get_keys_in_slot�.��	       �epoch�.��       �cluster_set_config_epoch�.��6       �2ClusterManagementCommands.cluster_set_config_epoch�.��	       �state�.��       �cluster_setslot�.��-       �)ClusterManagementCommands.cluster_setslot�.��       �cluster_setslot_stable�.��4       �0ClusterManagementCommands.cluster_setslot_stable�.��       �cluster_replicas�.��.       �*ClusterManagementCommands.cluster_replicas�.��       �
cluster_slots�.��+       �'ClusterManagementCommands.cluster_slots�.��       �cluster_shards�.��,       �(ClusterManagementCommands.cluster_shards�.��       �cluster_myshardid�.��/       �+ClusterManagementCommands.cluster_myshardid�.��       �
cluster_links�.��+       �'ClusterManagementCommands.cluster_links�.��       �cluster_flushslots�.��0       �,ClusterManagementCommands.cluster_flushslots�.��       �cluster_bumpepoch�.��/       �+ClusterManagementCommands.cluster_bumpepoch�.��       �readonly�.��&       �"ClusterManagementCommands.readonly�.��
       �	readwrite�.��'       �#ClusterManagementCommands.readwrite�.��       �gears_refresh_cluster�.��3       �/ClusterManagementCommands.gears_refresh_cluster�.��"       �AsyncClusterManagementCommands�.��       �ClusterDataAccessCommands�.���       ��
    A class for Redis Cluster Data Access Commands

    The class inherits from Redis's core DataAccessCommand class and do the
    required adjustments to work with cluster mode
    �.��       (�strings���N�t�.��       �algo�.��       �LCS�.��
       �value1�.��
       �value2�.��       �specific_argument�.��       �len�.��       �idx�.��       �minmatchlen�.��       �withmatchlen�.��%       �!ClusterDataAccessCommands.stralgo�.��       NNN��.��"       �AsyncClusterDataAccessCommands�.��       �RedisClusterCommands�.���      X�  
    A class for all Redis Cluster commands

    For key-based commands, the target node(s) will be internally determined
    by the keys' hash slot.
    Non-key-based commands can be executed with the 'target_nodes' argument to
    target specific nodes. By default, if target_nodes is not specified, the
    command will be executed on the default cluster node.

    :param :target_nodes: type can be one of the followings:
        - nodes flag: ALL_NODES, PRIMARIES, REPLICAS, RANDOM
        - 'ClusterNode'
        - 'list(ClusterNodes)'
        - 'dict(any:clusterNodes)'

    for example:
        r.cluster_info(target_nodes=RedisCluster.ALL_NODES)
    �.��       �AsyncRedisClusterCommands�.��       �redis\commands\cluster.py�.��       �.0�jk  h��.��#       �<module redis.commands.cluster>�.��       h���.��       (hhh7hht�.��       (hh-hjZ  jk  t�.��       (hh1h�pair�jk  t�.��       (hh-h7j+  �results�t�.��       (hhh-ht�.��       hjC  h���.��       hh��.��
       hjN  ��.��
       hjK  ��.��       hh���.��       hjC  jU  ��.��       hjk  jl  ��.��
       hjZ  ��.��
       hjC  ��.��       (hj]  j^  ht�.��       h��.��       hjN  h��.��       hhjN  ��.��       hjf  h��.��       hjo  h��.��       (hjC  jN  jK  jr  t�.��       hh-��.��       hh���.��       (hh-h.h�res�t�.��	       hh1h��.��	       hh.h���.��6       (hh�h�h�h�h��data��value��nodes��name�h��cur�t�.��&       (hh�h�h�h�h�j�  j�  j�  h�j�  t�.��4       (hj�  j�  j�  j�  j�  j�  j�  j�  h�hh�t�.��       �__spec__�.