# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['C:\\Program Files\\Python312\\python.exe', '-W', 'ignore', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\nuitka\\build\\INLINE~1\\bin\\scons.py', '--quiet', '-f', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\nuitka\\build\\BACKEN~1.SCO', '--jobs', '24', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\nuitka\\build', 'python_version=3.12', 'python_prefix=C:\\Program Files\\Python312', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'assume_yes_for_downloads=true', 'console_mode=force', 'noelf_mode=true', 'cpp_defines=_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'gil_mode=true', 'source_dir=.', 'nuitka_python=false', 'debug_mode=false', 'debugger_mode=false', 'python_debug=false', 'full_compat=false', 'trace_mode=false', 'file_reference_mode=runtime', 'compiled_module_count=415', 'result_exe=E:\\code\\orders\\安全教~1\\dist\\START_~1.DIS\\start_web.dll', 'frozen_modules=155', 'python_sysflag_no_site=true'],
    env={'PYTHONHASHSEED': '0','USERDOMAIN_ROAMINGPROFILE': 'moxiaoying','PROCESSOR_LEVEL': '6','NVM_SYMLINK': 'C:\\nvm4w\\nodejs','SESSIONNAME': 'Console','ALLUSERSPROFILE': 'C:\\ProgramData','PROCESSOR_ARCHITECTURE': 'AMD64','EFC_9340_3789132940': '1','PSMODULEPATH': 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules','SYSTEMDRIVE': 'C:','USERNAME': 'moxiaoying','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','ONEDRIVECONSUMER': 'C:\\Users\\<USER>\\OneDrive','PROGRAMDATA': 'C:\\ProgramData','ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined','PROGRAMW6432': 'C:\\Program Files','HOMEPATH': '\\Users\\76809','PROCESSOR_IDENTIFIER': 'Intel64 Family 6 Model 183 Stepping 1, GenuineIntel','PROGRAMFILES': 'C:\\Program Files','PUBLIC': 'C:\\Users\\<USER>\\Windows','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','USERDOMAIN': 'moxiaoying','LOGONSERVER': '\\\\MOXIAOYING','HTTP_PROXY': 'http://127.0.0.1:7897','EFC_9340_2775293581': '1','EFC_9340_2283032206': '1','ONEDRIVE': 'C:\\Users\\<USER>\\OneDrive','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','HTTPS_PROXY': 'http://127.0.0.1:7897','EFC_9340_1262719628': '1','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','PATH': 'C:\\Program Files\\Python312\\Scripts\\;C:\\Program Files\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Docker\\Docker\\resources\\bin;E:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\dotnet\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;;C:\\Program Files (x86)\\Tencent\\微信web开发者工具\\dll;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin','HYOFFICEAI_PATH': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\HaiYingSoft\\OfficeAI','NO_PROXY': 'localhost, 127.0.0.1, ::1','OS': 'Windows_NT','COMPUTERNAME': 'MOXIAOYING','NVM_HOME': 'C:\\Users\\<USER>\\AppData\\Local\\nvm','PROCESSOR_REVISION': 'b701','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','COMSPEC': 'C:\\Windows\\system32\\cmd.exe','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','SYSTEMROOT': 'C:\\Windows','HOMEDRIVE': 'C:','USERPROFILE': 'C:\\Users\\<USER>\\Users\\76809\\AppData\\Local\\Temp','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','NUMBER_OF_PROCESSORS': '24','EFC_9340_1592913036': '1','NUITKA_PYTHON_EXE_PATH': 'C:\\Program Files\\Python312\\python.exe','NUITKA_PACKAGE_DIR': 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\nuitka','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_BUILD_DEFINITIONS_CATALOG': '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)