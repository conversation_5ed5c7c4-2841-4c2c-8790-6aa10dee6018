# 安全教育平台Web版 v2.0

一个功能强大的安全教育平台自动化工具，提供Web可视化界面，支持学生、教师和管理员三种模式的自动学习。

## 🌟 功能特点

- ✅ **Web可视化界面** - 实时显示学习进度，操作简单直观
- ✅ **多角色支持** - 支持学生、教师、管理员三种模式
- ✅ **自动化学习** - 全自动完成所有学习任务
- ✅ **错误处理** - 完善的错误处理和重试机制
- ✅ **实时监控** - 实时显示学习进度和日志信息
- ✅ **加密保护** - 核心代码已编译加密，防止逆向工程
- ✅ **一键启动** - 双击即可运行，无需复杂配置

## 🚀 快速开始

### 方法一：使用发布包（推荐）

1. **下载发布包**
   - 下载 `安全教育平台_Web版_发布包.zip`
   - 解压到任意目录

2. **启动程序**
   - 双击运行 `启动程序.bat`
   - 等待浏览器自动打开

3. **开始使用**
   - 在Web界面中填写账号信息
   - 选择角色类型
   - 点击开始任务

### 方法二：源码运行

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **启动Web界面**
   ```bash
   python start_web.py
   ```
   或者双击运行 `一键启动.bat`

3. **打开浏览器**
   访问 http://localhost:5000

## 🎯 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务
- 自动重置学生密码（如果需要）

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生
- 自动重置教师和学生密码（如果需要）

## 📁 项目结构

```
安全教育平台/
├── app.py                    # Web应用主文件
├── start_web.py             # Web启动脚本
├── index.py                 # 核心业务逻辑
├── database.py              # 数据库操作
├── templates/               # Web模板文件
│   └── index_new.html      # 主界面模板
├── requirements.txt         # 依赖包列表
├── 一键启动.bat             # Windows一键启动脚本
├── start.bat               # Windows启动菜单
└── 安全教育平台_Web版_发布包/ # 打包后的发布版本
    ├── 安全教育平台_Web版.exe
    ├── 启动程序.bat
    ├── 一键启动.bat
    └── 使用说明.txt
```

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python环境是否正确安装
   - 确认所有依赖包已安装
   - 检查防火墙设置

2. **浏览器无法打开**
   - 手动访问 http://localhost:5000
   - 检查端口5000是否被占用
   - 尝试使用其他浏览器

3. **登录失败**
   - 检查账号密码是否正确
   - 确认网络连接正常
   - 检查是否频繁登录被限制

4. **学习任务失败**
   - 查看日志中的具体错误信息
   - 检查网络连接稳定性
   - 确认账号状态正常

## 📦 打包说明

本项目已提供打包后的可执行文件：

- **文件名**: `安全教育平台_Web版_发布包.zip`
- **大小**: 约17MB
- **特点**: 
  - 单文件可执行，无需安装Python
  - 核心代码已加密编译
  - 包含完整的Web界面
  - 一键启动，开箱即用

## 🛡️ 安全说明

- 本程序已进行代码编译和加密处理
- 不会收集或泄露用户个人信息
- 仅用于自动化学习，请合理使用
- 建议在安全的网络环境中使用

## 📋 系统要求

- **操作系统**: Windows 10/11
- **内存**: 至少 2GB RAM
- **网络**: 稳定的互联网连接
- **浏览器**: Chrome、Firefox、Edge等现代浏览器

## 🔄 更新日志

### v2.0 (当前版本)
- ✅ 移除命令行版本，专注Web界面
- ✅ 优化用户界面和交互体验
- ✅ 增强代码加密和安全保护
- ✅ 简化部署和使用流程
- ✅ 提供一键启动功能

### v1.x
- 支持命令行和Web双模式
- 基础的自动化学习功能

## 📞 技术支持

如遇到问题，请：
1. 查看使用说明文档
2. 检查程序运行日志
3. 确认网络环境正常
4. 验证账号信息正确

---

**版本**: v2.0  
**更新时间**: 2024年  
**适用系统**: Windows 10/11  
**许可证**: MIT License
