#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重新打包脚本 - 修复可执行文件问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 已清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"🧹 已清理文件: {spec_file}")

def build_web_app():
    """使用PyInstaller重新编译Web应用"""
    print("🔨 重新编译Web应用...")
    
    # 更详细的PyInstaller编译命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',  # 改为控制台模式，便于调试
        '--name=安全教育平台_Web版',
        '--add-data=templates;templates',
        '--add-data=data.db;.' if os.path.exists('data.db') else '',
        '--hidden-import=flask',
        '--hidden-import=flask_socketio',
        '--hidden-import=socketio',
        '--hidden-import=requests',
        '--hidden-import=loguru',
        '--hidden-import=bs4',
        '--hidden-import=lxml',
        '--hidden-import=urllib3',
        '--hidden-import=sqlite3',
        '--hidden-import=threading',
        '--hidden-import=webbrowser',
        '--hidden-import=time',
        '--hidden-import=pathlib',
        '--collect-all=flask',
        '--collect-all=flask_socketio',
        'start_web.py'
    ]
    
    # 移除空的参数
    cmd = [arg for arg in cmd if arg]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Web应用编译成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Web应用编译失败: {e}")
        return False

def update_package():
    """更新发布包"""
    print("📋 更新发布包...")
    
    package_dir = Path('安全教育平台_Web版_发布包')
    
    # 复制新的可执行文件
    exe_file = Path('dist/安全教育平台_Web版.exe')
    if exe_file.exists():
        if (package_dir / '安全教育平台_Web版.exe').exists():
            (package_dir / '安全教育平台_Web版.exe').unlink()
        shutil.copy2(exe_file, package_dir)
        print(f"✅ 已更新: {exe_file.name}")
        return True
    else:
        print("❌ 找不到新的可执行文件")
        return False

def create_improved_launcher(package_dir):
    """创建改进的启动脚本"""
    print("🚀 创建改进的启动脚本...")
    
    launcher_content = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版

echo.
echo ========================================
echo   安全教育平台Web版 v2.0
echo ========================================
echo.

echo 🔍 检查程序文件...
if not exist "%~dp0安全教育平台_Web版.exe" (
    echo ❌ 错误：找不到程序文件
    echo 📁 请确保此脚本与程序文件在同一目录下
    pause
    exit /b 1
)
echo ✅ 程序文件检查通过

echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.

echo 🚀 正在启动程序...
cd /d "%~dp0"
start "安全教育平台Web版" "%~dp0安全教育平台_Web版.exe"

echo.
echo ⏳ 等待程序启动...
timeout /t 5 /nobreak >nul

echo ✅ 程序已启动
echo 📱 请在浏览器中访问: http://localhost:5000
echo.
echo 💡 使用提示：
echo    • 如果浏览器没有自动打开，请手动访问上述地址
echo    • 程序窗口会显示详细的运行日志
echo    • 如需停止程序，请关闭程序窗口
echo.
pause
'''
    
    with open(package_dir / '启动程序.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 启动脚本已更新")

def create_debug_launcher(package_dir):
    """创建调试启动脚本"""
    print("🔧 创建调试启动脚本...")
    
    debug_content = '''@echo off
chcp 65001 >nul
title 安全教育平台Web版 - 调试模式

echo.
echo ========================================
echo   安全教育平台Web版 - 调试模式
echo ========================================
echo.

echo 🔧 调试模式启动...
echo 📋 此模式会显示详细的错误信息
echo.

cd /d "%~dp0"
"%~dp0安全教育平台_Web版.exe"

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
'''
    
    with open(package_dir / '调试启动.bat', 'w', encoding='utf-8') as f:
        f.write(debug_content)
    
    print("✅ 调试启动脚本已创建")

def update_zip_package(package_dir):
    """更新ZIP压缩包"""
    print("📦 更新ZIP压缩包...")
    
    zip_name = f'{package_dir.name}.zip'
    
    # 删除旧的压缩包
    if os.path.exists(zip_name):
        os.remove(zip_name)
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 压缩包已更新: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("🔧 安全教育平台Web版重新打包工具")
    print("=" * 50)
    
    # 清理构建目录
    clean_build_dirs()
    
    # 重新编译Web应用
    if not build_web_app():
        return False
    
    # 更新发布包
    package_dir = Path('安全教育平台_Web版_发布包')
    if not package_dir.exists():
        print("❌ 发布包目录不存在")
        return False
    
    if not update_package():
        return False
    
    # 创建改进的启动脚本
    create_improved_launcher(package_dir)
    
    # 创建调试启动脚本
    create_debug_launcher(package_dir)
    
    # 更新ZIP压缩包
    zip_file = update_zip_package(package_dir)
    
    print("\n🎉 重新打包完成！")
    print(f"📁 发布包目录: {package_dir}")
    print(f"📦 压缩包: {zip_file}")
    print(f"📏 压缩包大小: {os.path.getsize(zip_file) / 1024 / 1024:.1f} MB")
    print("\n📋 使用方法:")
    print("1. 解压ZIP文件到任意目录")
    print("2. 双击运行 '启动程序.bat'")
    print("3. 如果有问题，可以运行 '调试启动.bat' 查看错误信息")
    print("\n🔧 改进内容:")
    print("- 🔄 重新编译可执行文件")
    print("- 📝 改为控制台模式，便于查看错误")
    print("- 🚀 改进启动脚本逻辑")
    print("- 🔧 添加调试启动脚本")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ 重新打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 重新打包失败: {e}")
        sys.exit(1)
