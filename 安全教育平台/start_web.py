#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全教育平台Web界面启动脚本
"""

import os
import sys
import webbrowser
import time
import threading
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    # 在打包后的环境中，跳过依赖检查
    if hasattr(sys, '_MEIPASS'):
        print("✅ 运行在打包环境中，跳过依赖检查")
        return True

    required_packages = [
        ('flask', 'flask'),
        ('flask_socketio', 'flask_socketio'),
        ('requests', 'requests'),
        ('loguru', 'loguru'),
        ('bs4', 'beautifulsoup4')
    ]

    missing_packages = []

    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 请运行以下命令安装依赖:")
        print("   pdm install")
        print("\n或者使用pip:")
        print("   pip install -r requirements.txt")
        print("\n或者单独安装:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    return True

def create_templates_dir():
    """创建templates目录（如果不存在）"""
    templates_dir = Path("templates")
    if not templates_dir.exists():
        templates_dir.mkdir()
        print("📁 已创建templates目录")

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 已在浏览器中打开Web界面")
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")
        print("📱 请手动在浏览器中访问: http://localhost:5000")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 安全教育平台Web界面启动器")
    print("=" * 60)
    
    # 检查依赖
    print("🔍 检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ 依赖检查通过")
    
    # 创建必要目录
    create_templates_dir()
    
    # 检查关键文件
    required_files = ['app.py', 'index.py', 'templates/index.html']
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下关键文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\n请确保所有文件都在正确的位置")
        sys.exit(1)
    
    print("✅ 文件检查通过")
    
    # 启动浏览器（在新线程中）
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动Flask应用
    print("\n🌟 启动Web服务器...")
    print("📱 Web界面地址: http://localhost:5000")
    print("⚡ 按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 导入并启动app
        from app import app, socketio
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止，感谢使用！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查端口5000是否被占用")
        print("2. 确保所有依赖都已正确安装")
        print("3. 检查防火墙设置")
        sys.exit(1)

if __name__ == '__main__':
    main()
