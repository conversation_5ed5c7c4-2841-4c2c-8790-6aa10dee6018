<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全教育平台 - 自动化学习工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
            min-height: calc(100vh - 40px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            color: #495057;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }
        
        .nav-tabs {
            border-bottom: 3px solid #e9ecef;
            margin-bottom: 30px;
        }
        
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 600;
            padding: 15px 25px;
            margin-right: 10px;
            border-radius: 10px 10px 0 0;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }
        
        .nav-tabs .nav-link.active {
            background: #667eea;
            color: white;
            border-bottom: 3px solid #667eea;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }
        
        .card-body {
            padding: 25px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 12px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 15px;
            border-radius: 10px;
            height: 300px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .progress {
            height: 10px;
            border-radius: 10px;
            background: #e9ecef;
        }
        
        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-idle { background: #6c757d; color: white; }
        .status-running { background: #28a745; color: white; }
        .status-error { background: #dc3545; color: white; }
        .status-completed { background: #007bff; color: white; }
        
        .list-group-item {
            border-radius: 10px !important;
            border: 1px solid #e9ecef;
            margin-bottom: 10px;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .badge {
            padding: 6px 12px;
            border-radius: 20px;
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .feature-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-5px);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 响应式布局 */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 10px;
            }

            .card {
                margin-bottom: 15px;
            }

            .card-body {
                padding: 15px;
            }

            .row {
                margin: 0;
            }

            .col-lg-6, .col-md-6, .col-md-4 {
                padding: 5px;
                margin-bottom: 10px;
            }

            .btn {
                font-size: 0.875rem;
                padding: 8px 12px;
            }

            .form-control, .form-select {
                font-size: 16px; /* 防止iOS缩放 */
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .nav-tabs .nav-link {
                font-size: 0.875rem;
                padding: 8px 12px;
            }

            .log-container {
                height: 200px;
                font-size: 0.75rem;
            }

            .progress {
                height: 25px;
            }

            .progress-bar {
                font-size: 0.875rem;
                line-height: 25px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .header p {
                font-size: 0.875rem;
            }
        }

        @media (max-width: 576px) {
            .container-fluid {
                padding: 5px;
            }

            .card-header h5 {
                font-size: 1rem;
            }

            .btn-group .btn {
                font-size: 0.75rem;
                padding: 6px 8px;
            }

            .table th, .table td {
                padding: 0.5rem 0.25rem;
                font-size: 0.75rem;
            }

            .badge {
                font-size: 0.625rem;
            }

            .alert {
                font-size: 0.875rem;
                padding: 10px;
            }

            .nav-tabs {
                flex-wrap: nowrap;
                overflow-x: auto;
            }

            .nav-tabs .nav-link {
                white-space: nowrap;
                font-size: 0.75rem;
                padding: 6px 10px;
            }

            .header h1 {
                font-size: 1.25rem;
            }

            .col-md-4, .col-md-6, .col-md-8 {
                margin-bottom: 15px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn {
                min-height: 44px; /* 触摸友好的最小高度 */
            }

            .form-control, .form-select {
                min-height: 44px;
            }

            .nav-tabs .nav-link {
                min-height: 44px;
                display: flex;
                align-items: center;
            }
        }

        /* 横屏模式优化 */
        @media (max-width: 768px) and (orientation: landscape) {
            .log-container {
                height: 150px;
            }

            .card-body {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-shield-check"></i> 安全教育平台</h1>
                <p>自动化学习工具 - 让学习更简单高效</p>
            </div>
            
            <!-- 标签页导航 -->
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="task-tab" data-bs-toggle="tab" data-bs-target="#task-pane" type="button" role="tab">
                        <i class="bi bi-play-circle"></i> 任务执行
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-pane" type="button" role="tab">
                        <i class="bi bi-people"></i> 用户管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="teachers-tab" data-bs-toggle="tab" data-bs-target="#teachers-pane" type="button" role="tab">
                        <i class="bi bi-person-workspace"></i> 教师管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="projects-tab" data-bs-toggle="tab" data-bs-target="#projects-pane" type="button" role="tab">
                        <i class="bi bi-folder-check"></i> 项目管理
                    </button>
                </li>
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content" id="mainTabContent">
                <!-- 任务执行标签页 -->
                <div class="tab-pane fade show active" id="task-pane" role="tabpanel">
                    <div class="row">
                        <!-- 配置设置 -->
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-gear"></i> 配置设置</h5>
                                </div>
                                <div class="card-body">
                                    <form id="configForm">
                                        <div class="mb-3">
                                            <label class="form-label">快速选择用户</label>
                                            <select class="form-select" id="savedUserSelect">
                                                <option value="">手动输入或选择已保存的用户</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">用户名</label>
                                            <input type="text" class="form-control" id="username" placeholder="请输入用户名">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">密码</label>
                                            <input type="text" class="form-control" id="password" placeholder="请输入密码">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">角色</label>
                                            <select class="form-select" id="role">
                                                <option value="student">学生</option>
                                                <option value="teacher">教师</option>
                                                <option value="admin">管理员</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">选择项目</label>
                                            <select class="form-select" id="specialId">
                                                <option value="1178">默认项目 (1178)</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">从指定教师开始 (可选)</label>
                                            <div class="position-relative">
                                                <input type="text" class="form-control" id="startFromTeacher" placeholder="搜索教师用户名...">
                                                <div class="dropdown-menu" id="teacherDropdown" style="width: 100%; max-height: 200px; overflow-y: auto;">
                                                    <!-- 教师选项将在这里动态生成 -->
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="resumeFromCheckpoint">
                                                <label class="form-check-label" for="resumeFromCheckpoint">
                                                    从上次中断处继续
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary" id="startBtn">
                                                <i class="bi bi-play-fill"></i> 开始学习
                                            </button>
                                            <button type="button" class="btn btn-danger" id="stopBtn" disabled>
                                                <i class="bi bi-stop-fill"></i> 停止学习
                                            </button>
                                            <button type="button" class="btn btn-secondary" id="saveConfigBtn">
                                                <i class="bi bi-save"></i> 保存配置
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 任务状态 -->
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-activity"></i> 任务状态</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>执行进度</span>
                                            <span id="progressText">0%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>当前状态：</strong>
                                        <span class="status-badge status-idle" id="statusBadge">待机中</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>当前用户：</strong>
                                        <span id="currentUser">-</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>已完成：</strong>
                                        <span id="completedCount">0</span> / <span id="totalCount">0</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>开始时间：</strong>
                                        <span id="startTime">-</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>预计剩余：</strong>
                                        <span id="estimatedTime">-</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 实时日志 -->
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="bi bi-terminal"></i> 实时日志</h5>
                                    <button class="btn btn-sm btn-outline-light" id="clearLogsBtn">
                                        <i class="bi bi-trash"></i> 清空
                                    </button>
                                </div>
                                <div class="card-body p-0">
                                    <div class="log-container" id="logContainer">
                                        <div>等待任务开始...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理标签页 -->
                <div class="tab-pane fade" id="users-pane" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person-plus"></i> 用户管理</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 用户表单 -->
                                <div class="col-lg-6">
                                    <form id="userForm">
                                        <input type="hidden" id="userId">
                                        <div class="mb-3">
                                            <label class="form-label">用户名称</label>
                                            <input type="text" class="form-control" id="userName" placeholder="请输入用户名称">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">登录账号</label>
                                            <input type="text" class="form-control" id="userUsername" placeholder="请输入登录账号">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">登录密码</label>
                                            <input type="text" class="form-control" id="userPassword" placeholder="请输入登录密码">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">角色类型</label>
                                            <select class="form-select" id="userRole">
                                                <option value="student">学生</option>
                                                <option value="teacher">教师</option>
                                                <option value="admin">管理员</option>
                                            </select>
                                        </div>
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary" id="saveUserBtn">
                                                <i class="bi bi-save"></i> 保存用户
                                            </button>
                                            <button type="button" class="btn btn-secondary" id="resetUserFormBtn">
                                                <i class="bi bi-arrow-clockwise"></i> 重置表单
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- 已保存用户列表 -->
                                <div class="col-lg-6">
                                    <h6><i class="bi bi-people"></i> 已保存用户</h6>
                                    <div id="savedUsersList" class="list-group">
                                        <div class="list-group-item text-center text-muted">暂无保存的用户</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 教师管理标签页 -->
                <div class="tab-pane fade" id="teachers-pane" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person-workspace"></i> 教师管理</h5>
                        </div>
                        <div class="card-body">
                            <!-- 管理员选择 -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <label class="form-label">选择管理员</label>
                                    <select class="form-select" id="adminSelect">
                                        <option value="">手动输入管理员信息</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">管理员账号</label>
                                    <input type="text" class="form-control" id="adminUsername" placeholder="请输入管理员账号">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">管理员密码</label>
                                    <input type="text" class="form-control" id="adminPassword" placeholder="请输入管理员密码">
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-primary w-100" id="fetchTeachersBtn">
                                        <i class="bi bi-download"></i> 获取教师列表
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-secondary w-100" id="clearTeachersBtn">
                                        <i class="bi bi-trash"></i> 清空列表
                                    </button>
                                </div>
                            </div>

                            <!-- 多选教师功能 -->
                            <div class="mb-4">
                                <h6><i class="bi bi-check2-square"></i> 多选教师执行</h6>
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" id="teacherSearchInput" placeholder="搜索教师用户名或姓名...">
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-success w-100" id="executeSelectedBtn" disabled>
                                            <i class="bi bi-play-fill"></i> 执行选中教师 (<span id="selectedCount">0</span>)
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 教师列表 -->
                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">教师列表 <span class="badge bg-primary" id="teacherCount">0</span></h6>
                                <button class="btn btn-sm btn-outline-primary" id="refreshTeachersBtn">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>

                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light sticky-top">
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" class="form-check-input" id="selectAllTeachers">
                                            </th>
                                            <th>用户名</th>
                                            <th>姓名</th>
                                            <th>学校</th>
                                            <th>班级/学生</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="teachersTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-4">
                                                <i class="bi bi-info-circle"></i> 请先使用管理员账号获取教师列表
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目管理标签页 -->
                <div class="tab-pane fade" id="projects-pane" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-folder-check"></i> 项目管理</h5>
                        </div>
                        <div class="card-body">
                            <!-- 管理员选择 -->
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label for="projectAdminSelect" class="form-label">选择管理员账号</label>
                                    <select class="form-select" id="projectAdminSelect">
                                        <option value="">请选择管理员账号</option>
                                    </select>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-info w-100" onclick="refreshProjectAdminList()">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新管理员列表
                                    </button>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-primary w-100" id="fetchProjectsBtn">
                                        <i class="bi bi-download"></i> 获取项目列表
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-secondary w-100" id="clearProjectsBtn">
                                        <i class="bi bi-trash"></i> 清空列表
                                    </button>
                                </div>
                            </div>

                            <!-- 项目筛选 -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label">筛选标题</label>
                                    <input type="text" class="form-control" id="projectTitleFilter" placeholder="搜索项目标题...">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">筛选状态</label>
                                    <select class="form-select" id="projectStatusFilter">
                                        <option value="">全部状态</option>
                                        <option value="active">可用</option>
                                        <option value="disabled">禁用</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 项目列表 -->
                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">项目列表 <span class="badge bg-primary" id="projectCount">0</span></h6>
                                <button class="btn btn-sm btn-outline-primary" id="refreshProjectsBtn">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>

                            <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light sticky-top">
                                        <tr>
                                            <th>项目标题</th>
                                            <th>Special ID</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="projectsTableBody">
                                        <tr>
                                            <td colspan="3" class="text-center text-muted py-4">
                                                <i class="bi bi-info-circle"></i> 暂无项目数据，请点击"获取项目列表"按钮
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 完成情况详情模态框 -->
    <div class="modal fade" id="completionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="completionModalTitle">项目完成情况</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">教师完成情况</h6>
                                    <div class="progress mb-2" style="height: 25px;">
                                        <div class="progress-bar" id="teacherProgressBar" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                    <small class="text-muted">
                                        已完成: <span id="completedTeachersCount">0</span> / 总计: <span id="totalTeachersCount">0</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">学生完成情况</h6>
                                    <div class="progress mb-2" style="height: 25px;">
                                        <div class="progress-bar bg-success" id="studentProgressBar" role="progressbar" style="width: 0%">0%</div>
                                    </div>
                                    <small class="text-muted">
                                        已完成: <span id="completedStudentsCount">0</span> / 总计: <span id="totalStudentsCount">0</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 未完成教师列表 -->
                    <div id="incompleteTeachersSection">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">未完成教师列表</h6>
                            <button type="button" class="btn btn-primary btn-sm" id="completeSelectedBtn" disabled>
                                <i class="bi bi-play-fill"></i> 完成选中教师任务
                            </button>
                        </div>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-sm table-hover">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input" id="selectAllIncomplete">
                                        </th>
                                        <th>教师用户名</th>
                                        <th>教师姓名</th>
                                        <th>学校</th>
                                        <th>教师完成</th>
                                        <th>学生完成率</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="incompleteTeachersBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="refreshCompletionBtn">
                        <i class="bi bi-arrow-clockwise"></i> 刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script>
        // 全局变量
        let socket = null;
        let teachersList = [];
        let selectedTeachers = [];
        let currentTaskId = null;
        let projectsList = [];
        let filteredProjectsList = [];
        let currentCompletionData = null;
        let selectedIncompleteTeachers = [];

        // DOM元素
        const elements = {
            // 任务执行相关
            savedUserSelect: document.getElementById('savedUserSelect'),
            username: document.getElementById('username'),
            password: document.getElementById('password'),
            role: document.getElementById('role'),
            specialId: document.getElementById('specialId'),
            startFromTeacher: document.getElementById('startFromTeacher'),
            resumeFromCheckpoint: document.getElementById('resumeFromCheckpoint'),
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            saveConfigBtn: document.getElementById('saveConfigBtn'),
            progressBar: document.getElementById('progressBar'),
            progressText: document.getElementById('progressText'),
            statusBadge: document.getElementById('statusBadge'),
            currentUser: document.getElementById('currentUser'),
            completedCount: document.getElementById('completedCount'),
            totalCount: document.getElementById('totalCount'),
            startTime: document.getElementById('startTime'),
            estimatedTime: document.getElementById('estimatedTime'),
            logContainer: document.getElementById('logContainer'),
            clearLogsBtn: document.getElementById('clearLogsBtn'),
            teacherDropdown: document.getElementById('teacherDropdown'),

            // 用户管理相关
            userId: document.getElementById('userId'),
            userName: document.getElementById('userName'),
            userUsername: document.getElementById('userUsername'),
            userPassword: document.getElementById('userPassword'),
            userRole: document.getElementById('userRole'),
            saveUserBtn: document.getElementById('saveUserBtn'),
            resetUserFormBtn: document.getElementById('resetUserFormBtn'),
            savedUsersList: document.getElementById('savedUsersList'),

            // 教师管理相关
            adminSelect: document.getElementById('adminSelect'),
            adminUsername: document.getElementById('adminUsername'),
            adminPassword: document.getElementById('adminPassword'),
            fetchTeachersBtn: document.getElementById('fetchTeachersBtn'),
            clearTeachersBtn: document.getElementById('clearTeachersBtn'),
            refreshTeachersBtn: document.getElementById('refreshTeachersBtn'),
            teacherSearchInput: document.getElementById('teacherSearchInput'),
            teachersTableBody: document.getElementById('teachersTableBody'),
            teacherCount: document.getElementById('teacherCount'),
            selectAllTeachers: document.getElementById('selectAllTeachers'),
            executeSelectedBtn: document.getElementById('executeSelectedBtn'),
            selectedCount: document.getElementById('selectedCount'),

            // 项目管理相关
            projectAdminSelect: document.getElementById('projectAdminSelect'),
            fetchProjectsBtn: document.getElementById('fetchProjectsBtn'),
            clearProjectsBtn: document.getElementById('clearProjectsBtn'),
            refreshProjectsBtn: document.getElementById('refreshProjectsBtn'),
            projectsTableBody: document.getElementById('projectsTableBody'),
            projectCount: document.getElementById('projectCount'),
            projectTitleFilter: document.getElementById('projectTitleFilter'),
            projectStatusFilter: document.getElementById('projectStatusFilter'),

            // 完成情况模态框相关
            completionModal: document.getElementById('completionModal'),
            completionModalTitle: document.getElementById('completionModalTitle'),
            teacherProgressBar: document.getElementById('teacherProgressBar'),
            studentProgressBar: document.getElementById('studentProgressBar'),
            completedTeachersCount: document.getElementById('completedTeachersCount'),
            totalTeachersCount: document.getElementById('totalTeachersCount'),
            completedStudentsCount: document.getElementById('completedStudentsCount'),
            totalStudentsCount: document.getElementById('totalStudentsCount'),
            incompleteTeachersBody: document.getElementById('incompleteTeachersBody'),
            selectAllIncomplete: document.getElementById('selectAllIncomplete'),
            completeSelectedBtn: document.getElementById('completeSelectedBtn'),
            refreshCompletionBtn: document.getElementById('refreshCompletionBtn')
        };

        // 定期轮询任务状态（备用机制）
        function startStatusPolling() {
            setInterval(function() {
                if (currentTaskId || task_status_polling) {
                    fetch('/api/status')
                    .then(response => response.json())
                    .then(data => {
                        updateTaskStatus(data);
                    })
                    .catch(error => {
                        console.error('轮询状态失败:', error);
                    });
                }
            }, 2000); // 每2秒轮询一次
        }

        let task_status_polling = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');

            // 检查必要的DOM元素
            if (!elements.saveUserBtn) {
                console.error('关键DOM元素未找到，请检查HTML结构');
                return;
            }

            try {
                initEventListeners();
                initSocket();
                loadConfig();
                loadUsers();
                updateAdminSelect();
                loadProjects();
                updateProjectSelect();
                startStatusPolling(); // 启动状态轮询
                console.log('初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                showAlert('页面初始化失败，请刷新页面重试', 'danger');
            }
        });

        // 初始化Socket连接
        function initSocket() {
            try {
                if (typeof io !== 'undefined') {
                    socket = io();

                    socket.on('connect', function() {
                        console.log('Socket连接成功');
                    });

                    socket.on('status_update', function(data) {
                        console.log('收到状态更新:', data);
                        updateTaskStatus(data);
                    });

                    socket.on('log_update', function(data) {
                        console.log('收到日志更新:', data);
                        addLogMessage(data.message, 'info');
                    });

                    socket.on('connect_error', function(error) {
                        console.error('Socket连接失败:', error);
                    });
                } else {
                    console.warn('Socket.IO未加载，将使用轮询模式');
                    socket = null;
                }
            } catch (error) {
                console.error('Socket初始化失败:', error);
                socket = null;
            }
        }

        // 初始化事件监听器
        function initEventListeners() {
            console.log('初始化事件监听器...');

            // 检查关键元素是否存在
            const requiredElements = [
                'saveUserBtn', 'resetUserFormBtn', 'startBtn', 'stopBtn',
                'saveConfigBtn', 'clearLogsBtn', 'adminSelect', 'fetchTeachersBtn'
            ];

            for (const elementName of requiredElements) {
                if (!elements[elementName]) {
                    console.error(`关键元素 ${elementName} 未找到`);
                    return false;
                }
            }

            try {
                // 任务执行相关
                if (elements.startBtn) elements.startBtn.addEventListener('click', startTask);
                if (elements.stopBtn) elements.stopBtn.addEventListener('click', stopTask);
                if (elements.saveConfigBtn) elements.saveConfigBtn.addEventListener('click', saveConfig);
                if (elements.clearLogsBtn) elements.clearLogsBtn.addEventListener('click', clearLogs);
                if (elements.savedUserSelect) elements.savedUserSelect.addEventListener('change', loadUserToForm);

                // 用户管理相关
                if (elements.saveUserBtn) {
                    elements.saveUserBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('保存用户按钮点击事件触发');
                        saveUser();
                    });
                }
                if (elements.resetUserFormBtn) elements.resetUserFormBtn.addEventListener('click', resetUserForm);

                // 教师管理相关
                if (elements.adminSelect) elements.adminSelect.addEventListener('change', selectAdmin);
                if (elements.fetchTeachersBtn) elements.fetchTeachersBtn.addEventListener('click', fetchTeachers);
                if (elements.clearTeachersBtn) elements.clearTeachersBtn.addEventListener('click', clearTeachers);
                if (elements.refreshTeachersBtn) elements.refreshTeachersBtn.addEventListener('click', refreshTeachers);
                if (elements.teacherSearchInput) elements.teacherSearchInput.addEventListener('input', filterTeachers);
                if (elements.selectAllTeachers) elements.selectAllTeachers.addEventListener('change', selectAllTeachers);
                if (elements.executeSelectedBtn) elements.executeSelectedBtn.addEventListener('click', executeSelectedTeachers);

                // 项目管理相关
                if (elements.fetchProjectsBtn) elements.fetchProjectsBtn.addEventListener('click', fetchProjects);
                if (elements.clearProjectsBtn) elements.clearProjectsBtn.addEventListener('click', clearProjects);
                if (elements.refreshProjectsBtn) elements.refreshProjectsBtn.addEventListener('click', refreshProjects);
                if (elements.projectTitleFilter) elements.projectTitleFilter.addEventListener('input', filterProjects);
                if (elements.projectStatusFilter) elements.projectStatusFilter.addEventListener('change', filterProjects);

                // 完成情况相关
                if (elements.selectAllIncomplete) elements.selectAllIncomplete.addEventListener('change', selectAllIncompleteTeachers);
                if (elements.completeSelectedBtn) elements.completeSelectedBtn.addEventListener('click', completeSelectedTeachers);
                if (elements.refreshCompletionBtn) elements.refreshCompletionBtn.addEventListener('click', refreshCompletionData);

                console.log('事件监听器初始化完成');
                return true;
            } catch (error) {
                console.error('初始化事件监听器失败:', error);
                return false;
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // 任务执行相关函数
        function startTask() {
            const config = {
                username: elements.username.value.trim(),
                password: elements.password.value.trim(),
                role: elements.role.value,
                special_id: elements.specialId.value.trim(),
                start_from_teacher: elements.startFromTeacher.value.trim(),
                resume_from_checkpoint: elements.resumeFromCheckpoint.checked
            };

            if (!config.username || !config.password) {
                showAlert('请输入用户名和密码', 'warning');
                return;
            }

            fetch('/api/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    task_status_polling = true;
                    elements.startBtn.disabled = true;
                    elements.stopBtn.disabled = false;
                    showAlert('任务已开始', 'success');
                } else {
                    showAlert(data.message || '启动失败', 'danger');
                }
            })
            .catch(error => {
                console.error('启动任务失败:', error);
                showAlert('启动任务失败', 'danger');
            });
        }

        function stopTask() {
            if (!currentTaskId) {
                showAlert('没有正在运行的任务', 'warning');
                return;
            }

            fetch('/api/stop', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ task_id: currentTaskId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    task_status_polling = false;
                    currentTaskId = null;
                    elements.startBtn.disabled = false;
                    elements.stopBtn.disabled = true;
                    showAlert('任务已停止', 'info');
                } else {
                    showAlert(data.message || '停止失败', 'danger');
                }
            })
            .catch(error => {
                console.error('停止任务失败:', error);
                showAlert('停止任务失败', 'danger');
            });
        }

        function saveConfig() {
            const config = {
                username: elements.username.value.trim(),
                password: elements.password.value.trim(),
                role: elements.role.value,
                special_id: elements.specialId.value.trim(),
                start_from_teacher: elements.startFromTeacher.value.trim(),
                resume_from_checkpoint: elements.resumeFromCheckpoint.checked
            };

            fetch('/api/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('配置已保存', 'success');
                } else {
                    showAlert(data.message || '保存失败', 'danger');
                }
            })
            .catch(error => {
                console.error('保存配置失败:', error);
                showAlert('保存配置失败', 'danger');
            });
        }

        function loadConfig() {
            fetch('/api/config')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.config) {
                    const config = data.config;
                    elements.username.value = config.username || '';
                    elements.password.value = config.password || '';
                    elements.role.value = config.role || 'student';
                    elements.specialId.value = config.special_id || '1178';
                    elements.startFromTeacher.value = config.start_from_teacher || '';
                    elements.resumeFromCheckpoint.checked = config.resume_from_checkpoint || false;
                }
            })
            .catch(error => console.error('加载配置失败:', error));
        }

        function clearLogs() {
            elements.logContainer.innerHTML = '<div>日志已清空...</div>';
        }

        function addLogMessage(message, level = 'info') {
            const logDiv = document.createElement('div');
            logDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.className = `log-${level}`;
            elements.logContainer.appendChild(logDiv);
            elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
        }

        function updateTaskStatus(data) {
            console.log('更新任务状态:', data);

            // 更新状态显示
            if (data.current_task) {
                const statusText = data.running ? '运行中' : (data.current_task.includes('完成') ? '已完成' : '待机中');
                elements.statusBadge.textContent = statusText;

                // 更新状态样式
                elements.statusBadge.className = 'status-badge';
                if (data.running) {
                    elements.statusBadge.classList.add('status-running');
                } else if (data.current_task.includes('完成')) {
                    elements.statusBadge.classList.add('status-completed');
                } else if (data.current_task.includes('失败')) {
                    elements.statusBadge.classList.add('status-error');
                } else {
                    elements.statusBadge.classList.add('status-idle');
                }
            }

            // 更新进度
            if (data.progress !== undefined && data.total !== undefined) {
                const percentage = data.total > 0 ? Math.round((data.progress / data.total) * 100) : 0;
                elements.progressBar.style.width = percentage + '%';
                elements.progressText.textContent = percentage + '%';
                elements.completedCount.textContent = data.progress || 0;
                elements.totalCount.textContent = data.total || 0;
            }

            // 更新当前任务
            if (data.current_task) {
                elements.currentUser.textContent = data.current_task;
            }

            // 更新开始时间
            if (data.start_time) {
                elements.startTime.textContent = data.start_time;
            }

            // 更新预计时间
            if (data.estimated_time) {
                elements.estimatedTime.textContent = data.estimated_time;
            }

            // 任务完成时更新按钮状态
            if (!data.running) {
                elements.startBtn.disabled = false;
                elements.stopBtn.disabled = true;
            } else {
                elements.startBtn.disabled = true;
                elements.stopBtn.disabled = false;
            }
        }

        // 用户管理相关函数
        function loadUsers() {
            fetch('/api/users')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayUsers(data.users);
                    updateSavedUserSelect(data.users);
                }
            })
            .catch(error => console.error('加载用户失败:', error));
        }

        function displayUsers(users) {
            const container = elements.savedUsersList;

            if (users.length === 0) {
                container.innerHTML = '<div class="list-group-item text-center text-muted">暂无保存的用户</div>';
                return;
            }

            container.innerHTML = users.map(user => `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${user.name}</strong> <small class="text-muted">(${user.username})</small>
                        <br>
                        <span class="badge bg-${getRoleBadgeColor(user.role)} me-1">${getRoleDisplayName(user.role)}</span>
                        <small class="text-muted">${user.created_at}</small>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id}, '${user.name}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function updateSavedUserSelect(users) {
            const select = elements.savedUserSelect;
            select.innerHTML = '<option value="">手动输入或选择已保存的用户</option>';

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.username}) - ${getRoleDisplayName(user.role)}`;
                select.appendChild(option);
            });
        }

        function getRoleBadgeColor(role) {
            const colors = { student: 'primary', teacher: 'success', admin: 'danger' };
            return colors[role] || 'secondary';
        }

        function getRoleDisplayName(role) {
            const names = { student: '学生', teacher: '教师', admin: '管理员' };
            return names[role] || role;
        }

        function saveUser() {
            console.log('保存用户按钮被点击');

            try {
                const userData = {
                    id: elements.userId.value || null,
                    name: elements.userName.value.trim(),
                    username: elements.userUsername.value.trim(),
                    password: elements.userPassword.value.trim(),
                    role: elements.userRole.value
                };

                console.log('用户数据:', userData);

                if (!userData.name || !userData.username || !userData.password) {
                    showAlert('请填写完整的用户信息', 'warning');
                    return;
                }

                const url = userData.id ? `/api/users/${userData.id}` : '/api/users';
                const method = userData.id ? 'PUT' : 'POST';

                console.log('发送请求:', method, url);

                // 禁用按钮防止重复提交
                elements.saveUserBtn.disabled = true;
                elements.saveUserBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

                fetch(url, {
                    method: method,
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(userData)
                })
                .then(response => {
                    console.log('响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('响应数据:', data);
                    if (data.success) {
                        showAlert(data.message || '保存成功', 'success');
                        resetUserForm();
                        loadUsers();
                        updateAdminSelect();
                    } else {
                        showAlert(data.message || '保存失败', 'danger');
                    }
                })
                .catch(error => {
                    console.error('保存用户失败:', error);
                    showAlert('网络错误，保存用户失败', 'danger');
                })
                .finally(() => {
                    // 恢复按钮状态
                    elements.saveUserBtn.disabled = false;
                    elements.saveUserBtn.innerHTML = '<i class="bi bi-save"></i> 保存用户';
                });
            } catch (error) {
                console.error('saveUser函数执行失败:', error);
                showAlert('保存用户时发生错误', 'danger');
            }
        }

        function resetUserForm() {
            elements.userId.value = '';
            elements.userName.value = '';
            elements.userUsername.value = '';
            elements.userPassword.value = '';
            elements.userRole.value = 'student';
        }

        function loadUserToForm() {
            const userId = elements.savedUserSelect.value;
            if (!userId) return;

            fetch('/api/users')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const user = data.users.find(u => u.id == userId);
                    if (user) {
                        elements.username.value = user.username;
                        elements.password.value = user.password;
                        elements.role.value = user.role;
                    }
                }
            })
            .catch(error => console.error('加载用户信息失败:', error));
        }

        function editUser(userId) {
            fetch('/api/users')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const user = data.users.find(u => u.id === userId);
                    if (user) {
                        elements.userId.value = user.id;
                        elements.userName.value = user.name;
                        elements.userUsername.value = user.username;
                        elements.userPassword.value = user.password;
                        elements.userRole.value = user.role;
                    }
                }
            })
            .catch(error => console.error('加载用户信息失败:', error));
        }

        function deleteUser(userId, userName) {
            if (!confirm(`确定要删除用户 "${userName}" 吗？`)) return;

            fetch(`/api/users/${userId}`, { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    loadUsers();
                    updateAdminSelect();
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('删除用户失败:', error);
                showAlert('删除用户失败', 'danger');
            });
        }

        // 教师管理相关函数
        function updateAdminSelect() {
            fetch('/api/users')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const adminUsers = data.users.filter(user => user.role === 'admin');

                    // 更新教师管理的管理员选择
                    const teacherAdminSelect = elements.adminSelect;
                    teacherAdminSelect.innerHTML = '<option value="">手动输入管理员信息</option>';
                    adminUsers.forEach(admin => {
                        const option = document.createElement('option');
                        option.value = `${admin.username}|${admin.password}`;
                        option.textContent = `${admin.name} (${admin.username})`;
                        teacherAdminSelect.appendChild(option);
                    });

                    // 更新项目管理的管理员选择
                    const projectAdminSelect = elements.projectAdminSelect;
                    if (projectAdminSelect) {
                        projectAdminSelect.innerHTML = '<option value="">请选择管理员账号</option>';
                        adminUsers.forEach(admin => {
                            const option = document.createElement('option');
                            option.value = JSON.stringify({username: admin.username, password: admin.password});
                            option.textContent = `${admin.name} (${admin.username})`;
                            projectAdminSelect.appendChild(option);
                        });
                    }
                }
            })
            .catch(error => console.error('加载管理员列表失败:', error));
        }

        function selectAdmin() {
            const selectedValue = elements.adminSelect.value;
            if (selectedValue) {
                const [username, password] = selectedValue.split('|');
                elements.adminUsername.value = username;
                elements.adminPassword.value = password;
            } else {
                elements.adminUsername.value = '';
                elements.adminPassword.value = '';
            }
        }

        function fetchTeachers() {
            const adminUsername = elements.adminUsername.value.trim();
            const adminPassword = elements.adminPassword.value.trim();

            if (!adminUsername || !adminPassword) {
                showAlert('请输入管理员账号和密码', 'warning');
                return;
            }

            elements.fetchTeachersBtn.disabled = true;
            elements.fetchTeachersBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 获取中...';

            fetch('/api/teachers/fetch', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: adminUsername,
                    password: adminPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    teachersList = data.teachers;
                    displayTeachers(teachersList);
                    showAlert(`成功获取 ${teachersList.length} 个教师`, 'success');
                } else {
                    showAlert(data.message || '获取教师列表失败', 'danger');
                }
            })
            .catch(error => {
                console.error('获取教师列表失败:', error);
                showAlert('获取教师列表失败', 'danger');
            })
            .finally(() => {
                elements.fetchTeachersBtn.disabled = false;
                elements.fetchTeachersBtn.innerHTML = '<i class="bi bi-download"></i> 获取教师列表';
            });
        }

        function clearTeachers() {
            if (confirm('确定要清空教师列表吗？')) {
                teachersList = [];
                displayTeachers([]);
                showAlert('教师列表已清空', 'info');
            }
        }

        function refreshTeachers() {
            loadTeachers();
        }

        function loadTeachers() {
            const adminUsername = elements.adminUsername.value.trim();

            fetch(`/api/teachers?admin_username=${encodeURIComponent(adminUsername)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    teachersList = data.teachers;
                    displayTeachers(teachersList);
                }
            })
            .catch(error => {
                console.error('加载教师列表失败:', error);
            });
        }

        function displayTeachers(teachers) {
            const tbody = elements.teachersTableBody;
            elements.teacherCount.textContent = teachers.length;

            if (teachers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted py-4"><i class="bi bi-info-circle"></i> 暂无教师数据</td></tr>';
                return;
            }

            tbody.innerHTML = teachers.map(teacher => `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input" name="teacherSelect" value="${teacher.username}" onchange="updateSelectedTeachers()">
                    </td>
                    <td><code>${teacher.username}</code></td>
                    <td>${teacher.nickname || '未知'}</td>
                    <td>${teacher.school || '未知'}</td>
                    <td>
                        <span class="badge bg-info me-1">${teacher.class_count || 0}</span>
                        <span class="badge bg-success">${teacher.student_count || 0}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="selectTeacher('${teacher.username}')" title="选择为起始教师">
                            <i class="bi bi-check-circle"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

            selectedTeachers = [];
            updateSelectedTeachers();
        }

        function filterTeachers() {
            const searchTerm = elements.teacherSearchInput.value.toLowerCase();
            const filteredTeachers = teachersList.filter(teacher =>
                teacher.username.toLowerCase().includes(searchTerm) ||
                (teacher.nickname && teacher.nickname.toLowerCase().includes(searchTerm)) ||
                (teacher.school && teacher.school.toLowerCase().includes(searchTerm))
            );
            displayTeachers(filteredTeachers);
        }

        function selectAllTeachers() {
            const checkboxes = document.querySelectorAll('input[name="teacherSelect"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = elements.selectAllTeachers.checked;
            });
            updateSelectedTeachers();
        }

        function updateSelectedTeachers() {
            const checkboxes = document.querySelectorAll('input[name="teacherSelect"]:checked');
            selectedTeachers = Array.from(checkboxes).map(cb => cb.value);

            elements.selectedCount.textContent = selectedTeachers.length;
            elements.executeSelectedBtn.disabled = selectedTeachers.length === 0;

            const allCheckboxes = document.querySelectorAll('input[name="teacherSelect"]');
            if (allCheckboxes.length > 0) {
                elements.selectAllTeachers.checked = selectedTeachers.length === allCheckboxes.length;
                elements.selectAllTeachers.indeterminate = selectedTeachers.length > 0 && selectedTeachers.length < allCheckboxes.length;
            }
        }

        function selectTeacher(username) {
            elements.startFromTeacher.value = username;
            document.getElementById('task-tab').click();
            showAlert(`已选择教师: ${username}`, 'success');
        }

        function executeSelectedTeachers() {
            if (selectedTeachers.length === 0) {
                showAlert('请先选择要执行的教师', 'warning');
                return;
            }

            if (confirm(`确定要对选中的 ${selectedTeachers.length} 个教师执行学习任务吗？`)) {
                // 创建批量任务
                fetch('/api/execute-teachers', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        teachers: selectedTeachers,
                        admin_username: elements.adminUsername.value,
                        admin_password: elements.adminPassword.value,
                        special_id: elements.specialId.value || '1178'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentTaskId = data.task_id;
                        task_status_polling = true;
                        document.getElementById('task-tab').click();
                        showAlert(`批量任务已启动，任务ID: ${data.task_id}`, 'success');
                    } else {
                        showAlert(data.message || '启动批量任务失败', 'danger');
                    }
                })
                .catch(error => {
                    console.error('启动批量任务失败:', error);
                    showAlert('启动批量任务失败', 'danger');
                });
            }
        }

        // 项目管理相关函数
        function fetchProjects() {
            const selectedAdmin = elements.projectAdminSelect.value;

            if (!selectedAdmin) {
                showAlert('请先选择管理员账号', 'warning');
                return;
            }

            const adminData = JSON.parse(selectedAdmin);

            elements.fetchProjectsBtn.disabled = true;
            elements.fetchProjectsBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 获取中...';

            fetch('/api/projects/fetch', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: adminData.username,
                    password: adminData.password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    projectsList = data.projects;
                    filteredProjectsList = projectsList;
                    displayProjects(filteredProjectsList);
                    updateProjectSelect();
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.message || '获取项目列表失败', 'danger');
                }
            })
            .catch(error => {
                console.error('获取项目列表失败:', error);
                showAlert('网络错误，获取项目列表失败', 'danger');
            })
            .finally(() => {
                elements.fetchProjectsBtn.disabled = false;
                elements.fetchProjectsBtn.innerHTML = '<i class="bi bi-download"></i> 获取项目列表';
            });
        }

        function clearProjects() {
            if (!confirm('确定要清空项目列表吗？')) return;

            fetch('/api/projects/clear', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    projectsList = [];
                    displayProjects(projectsList);
                    updateProjectSelect();
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.message || '清空项目列表失败', 'danger');
                }
            })
            .catch(error => {
                console.error('清空项目列表失败:', error);
                showAlert('网络错误，清空项目列表失败', 'danger');
            });
        }

        function refreshProjects() {
            loadProjects();
        }

        function loadProjects() {
            fetch('/api/projects')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    projectsList = data.projects;
                    filteredProjectsList = projectsList;
                    displayProjects(filteredProjectsList);
                    updateProjectSelect();
                }
            })
            .catch(error => {
                console.error('加载项目列表失败:', error);
            });
        }

        function displayProjects(projects) {
            const tbody = elements.projectsTableBody;
            elements.projectCount.textContent = projects.length;

            if (projects.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="3" class="text-center text-muted py-4">
                            <i class="bi bi-info-circle"></i> 暂无项目数据
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = projects.map(project => `
                <tr class="${project.status === 'disabled' ? 'table-secondary' : ''}">
                    <td>
                        <span class="${project.status === 'disabled' ? 'text-muted' : ''}">${project.title}</span>
                        ${project.status === 'disabled' ? '<i class="bi bi-exclamation-triangle text-warning ms-1" title="未获取到Special ID"></i>' : ''}
                    </td>
                    <td>
                        ${project.special_id ? `<code>${project.special_id}</code>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td>
                        <span class="badge ${project.status === 'active' ? 'bg-success' : 'bg-secondary'}">
                            ${project.status === 'active' ? '可用' : '禁用'}
                        </span>
                    </td>
                </tr>
            `).join('');
        }

        function updateProjectSelect() {
            const specialIdSelect = elements.specialId;

            // 清空现有选项
            specialIdSelect.innerHTML = '<option value="">请选择项目</option>';

            // 添加可用的项目选项
            projectsList.filter(project => project.status === 'active' && project.special_id).forEach(project => {
                const option = document.createElement('option');
                option.value = project.special_id;
                option.textContent = `${project.title} (${project.special_id})`;
                specialIdSelect.appendChild(option);
            });
        }

        // 刷新项目管理的管理员列表
        function refreshProjectAdminList() {
            updateAdminSelect();
            showAlert('管理员列表已刷新', 'success');
        }

        // 项目筛选功能
        function filterProjects() {
            const titleFilter = elements.projectTitleFilter.value.toLowerCase();
            const statusFilter = elements.projectStatusFilter.value;

            filteredProjectsList = projectsList.filter(project => {
                const titleMatch = !titleFilter || project.title.toLowerCase().includes(titleFilter);
                const statusMatch = !statusFilter || project.status === statusFilter;

                return titleMatch && statusMatch;
            });

            displayProjects(filteredProjectsList);
        }

        // 显示完成情况详情
        function showCompletionDetails(specialId, title, adminUsername) {
            if (!adminUsername) {
                showAlert('管理员信息不可用', 'warning');
                return;
            }

            elements.completionModalTitle.textContent = `${title} - 完成情况`;

            // 显示加载状态
            elements.incompleteTeachersBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="spinner-border spinner-border-sm me-2"></div>
                        正在加载完成情况...
                    </td>
                </tr>
            `;

            // 显示模态框
            const modal = new bootstrap.Modal(elements.completionModal);
            modal.show();

            // 获取完成情况数据
            fetch(`/api/projects/${specialId}/completion`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    username: adminUsername
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentCompletionData = data.completion_status;
                    displayCompletionData(data.completion_status);
                } else {
                    showAlert(data.message || '获取完成情况失败', 'danger');
                    elements.incompleteTeachersBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center text-danger py-4">
                                <i class="bi bi-exclamation-triangle"></i> ${data.message || '获取完成情况失败'}
                            </td>
                        </tr>
                    `;
                }
            })
            .catch(error => {
                console.error('获取完成情况失败:', error);
                showAlert('网络错误，获取完成情况失败', 'danger');
                elements.incompleteTeachersBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-danger py-4">
                            <i class="bi bi-exclamation-triangle"></i> 网络错误，获取完成情况失败
                        </td>
                    </tr>
                `;
            });
        }

        function displayCompletionData(completionData) {
            // 更新进度条
            const teacherRate = completionData.teacher_completion_rate || 0;
            const studentRate = completionData.student_completion_rate || 0;

            elements.teacherProgressBar.style.width = teacherRate + '%';
            elements.teacherProgressBar.textContent = teacherRate + '%';
            elements.studentProgressBar.style.width = studentRate + '%';
            elements.studentProgressBar.textContent = studentRate + '%';

            // 更新统计数据
            elements.completedTeachersCount.textContent = completionData.completed_teachers_count || 0;
            elements.totalTeachersCount.textContent = completionData.total_teachers || 0;
            elements.completedStudentsCount.textContent = completionData.completed_students_count || 0;
            elements.totalStudentsCount.textContent = completionData.total_students || 0;

            // 显示未完成教师列表
            const incompleteTeachers = completionData.incomplete_teachers || [];

            if (incompleteTeachers.length === 0) {
                elements.incompleteTeachersBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-success py-4">
                            <i class="bi bi-check-circle"></i> 所有教师都已完成！
                        </td>
                    </tr>
                `;
                elements.completeSelectedBtn.disabled = true;
            } else {
                elements.incompleteTeachersBody.innerHTML = incompleteTeachers.map(teacher => `
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input incomplete-teacher-checkbox"
                                   value="${teacher.username}" onchange="updateSelectedIncompleteTeachers()">
                        </td>
                        <td><code>${teacher.username}</code></td>
                        <td>${teacher.nickname || '未知'}</td>
                        <td><small>${teacher.school || '未知'}</small></td>
                        <td>
                            <span class="badge ${teacher.teacher_completed ? 'bg-success' : 'bg-danger'}">
                                ${teacher.teacher_completed ? '已完成' : '未完成'}
                            </span>
                        </td>
                        <td>
                            <div class="progress" style="height: 18px;">
                                <div class="progress-bar ${teacher.completion_rate === 100 ? 'bg-success' : 'bg-warning'}"
                                     style="width: ${teacher.completion_rate}%">
                                    ${teacher.completion_rate}%
                                </div>
                            </div>
                            <small class="text-muted">${teacher.completed_students}/${teacher.student_count}</small>
                        </td>
                        <td>
                            ${teacher.error ?
                                `<span class="badge bg-danger" title="${teacher.error}">错误</span>` :
                                `<span class="badge ${teacher.all_completed ? 'bg-success' : 'bg-warning'}">
                                    ${teacher.all_completed ? '全部完成' : '进行中'}
                                </span>`
                            }
                        </td>
                    </tr>
                `).join('');

                selectedIncompleteTeachers = [];
                updateSelectedIncompleteTeachers();
            }
        }

        function selectAllIncompleteTeachers() {
            const checkboxes = document.querySelectorAll('.incomplete-teacher-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = elements.selectAllIncomplete.checked;
            });
            updateSelectedIncompleteTeachers();
        }

        function updateSelectedIncompleteTeachers() {
            const checkboxes = document.querySelectorAll('.incomplete-teacher-checkbox:checked');
            selectedIncompleteTeachers = Array.from(checkboxes).map(cb => cb.value);

            elements.completeSelectedBtn.disabled = selectedIncompleteTeachers.length === 0;

            const allCheckboxes = document.querySelectorAll('.incomplete-teacher-checkbox');
            if (allCheckboxes.length > 0) {
                elements.selectAllIncomplete.checked = selectedIncompleteTeachers.length === allCheckboxes.length;
                elements.selectAllIncomplete.indeterminate = selectedIncompleteTeachers.length > 0 && selectedIncompleteTeachers.length < allCheckboxes.length;
            }
        }

        function completeSelectedTeachers() {
            if (selectedIncompleteTeachers.length === 0) {
                showAlert('请先选择要完成的教师', 'warning');
                return;
            }

            if (!currentCompletionData) {
                showAlert('完成情况数据不可用', 'danger');
                return;
            }

            if (confirm(`确定要对选中的 ${selectedIncompleteTeachers.length} 个教师执行学习任务吗？`)) {
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(elements.completionModal);
                modal.hide();

                // 切换到任务执行页面
                document.getElementById('task-tab').click();

                // 设置special_id
                elements.specialId.value = currentCompletionData.special_id;

                // 创建批量任务
                fetch('/api/execute-teachers', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        teachers: selectedIncompleteTeachers,
                        admin_username: elements.projectAdminUsername.value,
                        admin_password: elements.projectAdminPassword.value,
                        special_id: currentCompletionData.special_id
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentTaskId = data.task_id;
                        task_status_polling = true;
                        showAlert(`批量任务已启动，任务ID: ${data.task_id}`, 'success');
                    } else {
                        showAlert(data.message || '启动批量任务失败', 'danger');
                    }
                })
                .catch(error => {
                    console.error('启动批量任务失败:', error);
                    showAlert('启动批量任务失败', 'danger');
                });
            }
        }

        function refreshCompletionData() {
            if (currentCompletionData) {
                showCompletionDetails(
                    currentCompletionData.special_id,
                    elements.completionModalTitle.textContent.split(' - ')[0],
                    elements.projectAdminUsername.value
                );
            }
        }
    </script>
</body>
</html>
